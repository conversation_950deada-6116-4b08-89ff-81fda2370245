Component: ARM Compiler 5.05 update 1 (build 106) Tool: armlink [4d0efa]

==============================================================================

Section Cross References

    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to main.o(i.TIM4_IRQHandler) for TIM4_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to main.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to mpu_exti.o(i.EXTI15_10_IRQHandler) for EXTI15_10_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_bkp.o(i.BKP_DeInit) refers to stm32f10x_rcc.o(i.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_can.o(i.CAN_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(i.CAN_GetITStatus) refers to stm32f10x_can.o(i.CheckITStatus) for CheckITStatus
    stm32f10x_cec.o(i.CEC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_flash.o(i.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) for FLASH_WaitForLastBank1Operation
    stm32f10x_flash.o(i.FLASH_EraseAllPages) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus) for FLASH_GetReadOutProtectionStatus
    stm32f10x_flash.o(i.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ErasePage) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ProgramWord) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(i.FLASH_GetBank1Status) for FLASH_GetBank1Status
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(i.I2C_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_pwr.o(i.PWR_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetAlarm) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetCounter) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_EnterConfigMode) for RTC_EnterConfigMode
    stm32f10x_rtc.o(i.RTC_SetPrescaler) refers to stm32f10x_rtc.o(i.RTC_ExitConfigMode) for RTC_ExitConfigMode
    stm32f10x_spi.o(i.I2S_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(i.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_wwdg.o(i.WWDG_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    pwm.o(i.PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OCStructInit) for TIM_OCStructInit
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC3Init) for TIM_OC3Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC4Init) for TIM_OC4Init
    pwm.o(i.PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    pwm.o(i.PWM_SetCompare3) refers to stm32f10x_tim.o(i.TIM_SetCompare3) for TIM_SetCompare3
    pwm.o(i.PWM_SetCompare4) refers to stm32f10x_tim.o(i.TIM_SetCompare4) for TIM_SetCompare4
    motor.o(i.Motor_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    motor.o(i.Motor_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    motor.o(i.Motor_Init) refers to pwm.o(i.PWM_Init) for PWM_Init
    motor.o(i.Motor_SetLeftSpeed) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_SetLeftSpeed) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_SetLeftSpeed) refers to pwm.o(i.PWM_SetCompare3) for PWM_SetCompare3
    motor.o(i.Motor_SetRightSpeed) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    motor.o(i.Motor_SetRightSpeed) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    motor.o(i.Motor_SetRightSpeed) refers to pwm.o(i.PWM_SetCompare4) for PWM_SetCompare4
    car.o(i.Car_Init) refers to motor.o(i.Motor_Init) for Motor_Init
    car.o(i.Car_Stop) refers to motor.o(i.Motor_SetLeftSpeed) for Motor_SetLeftSpeed
    car.o(i.Car_Stop) refers to motor.o(i.Motor_SetRightSpeed) for Motor_SetRightSpeed
    car.o(i.Go_Ahead) refers to motor.o(i.Motor_SetLeftSpeed) for Motor_SetLeftSpeed
    car.o(i.Go_Ahead) refers to motor.o(i.Motor_SetRightSpeed) for Motor_SetRightSpeed
    car.o(i.Go_Back) refers to motor.o(i.Motor_SetLeftSpeed) for Motor_SetLeftSpeed
    car.o(i.Go_Back) refers to motor.o(i.Motor_SetRightSpeed) for Motor_SetRightSpeed
    car.o(i.Self_Left) refers to motor.o(i.Motor_SetLeftSpeed) for Motor_SetLeftSpeed
    car.o(i.Self_Left) refers to motor.o(i.Motor_SetRightSpeed) for Motor_SetRightSpeed
    car.o(i.Self_Right) refers to motor.o(i.Motor_SetLeftSpeed) for Motor_SetLeftSpeed
    car.o(i.Self_Right) refers to motor.o(i.Motor_SetRightSpeed) for Motor_SetRightSpeed
    car.o(i.Turn_Left) refers to motor.o(i.Motor_SetLeftSpeed) for Motor_SetLeftSpeed
    car.o(i.Turn_Left) refers to motor.o(i.Motor_SetRightSpeed) for Motor_SetRightSpeed
    car.o(i.Turn_Right) refers to motor.o(i.Motor_SetLeftSpeed) for Motor_SetLeftSpeed
    car.o(i.Turn_Right) refers to motor.o(i.Motor_SetRightSpeed) for Motor_SetRightSpeed
    serial.o(i.Serial_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    serial.o(i.Serial_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    serial.o(i.Serial_Init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    serial.o(i.Serial_Init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    serial.o(i.Serial_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    serial.o(i.Serial_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    serial.o(i.Serial_Init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    serial.o(i.Serial_Printf) refers to vsprintf.o(.text) for vsprintf
    serial.o(i.Serial_Printf) refers to serial.o(i.Serial_SendString) for Serial_SendString
    serial.o(i.Serial_SendArray) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.Serial_SendByte) refers to stm32f10x_usart.o(i.USART_SendData) for USART_SendData
    serial.o(i.Serial_SendByte) refers to stm32f10x_usart.o(i.USART_GetFlagStatus) for USART_GetFlagStatus
    serial.o(i.Serial_SendNumber) refers to serial.o(i.Serial_Pow) for Serial_Pow
    serial.o(i.Serial_SendNumber) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.Serial_SendString) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    serial.o(i.fputc) refers to serial.o(i.Serial_SendByte) for Serial_SendByte
    track.o(i.Infrared_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    track.o(i.Infrared_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    track.o(i.track_start) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    track.o(i.track_start) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    timer.o(i.Timer1_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_InternalClockConfig) for TIM_InternalClockConfig
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_ClearFlag) for TIM_ClearFlag
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.Timer1_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    timer.o(i.Timer1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.Timer1_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) for dmp_read_fifo
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to asin.o(i.asin) for asin
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.MPU6050_DMP_Get_Data) refers to atan2.o(i.atan2) for atan2
    inv_mpu.o(i.MPU6050_DMP_Init) refers to mpu6050_i2c.o(i.MPU6050_IIC_IO_Init) for MPU6050_IIC_IO_Init
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_init) for mpu_init
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) for dmp_load_motion_driver_firmware
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.inv_orientation_matrix_to_scalar) for inv_orientation_matrix_to_scalar
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) for dmp_set_orientation
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) for dmp_enable_feature
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) for dmp_set_fifo_rate
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.run_self_test) for run_self_test
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.MPU6050_DMP_Init) refers to inv_mpu.o(.data) for gyro_orientation
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(i.get_accel_prod_shift) for get_accel_prod_shift
    inv_mpu.o(i.accel_self_test) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.accel_self_test) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.accel_self_test) refers to feqf.o(x$fpl$feqf) for __aeabi_cfcmpeq
    inv_mpu.o(i.accel_self_test) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.accel_self_test) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    inv_mpu.o(i.accel_self_test) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    inv_mpu.o(i.accel_self_test) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.get_accel_prod_shift) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.get_accel_prod_shift) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.get_accel_prod_shift) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.get_st_biases) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.get_st_biases) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.get_st_biases) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.get_st_biases) refers to llsdiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.get_st_biases) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.gyro_self_test) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.gyro_self_test) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.gyro_self_test) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.gyro_self_test) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    inv_mpu.o(i.gyro_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.gyro_self_test) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.gyro_self_test) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    inv_mpu.o(i.gyro_self_test) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.gyro_self_test) refers to inv_mpu.o(.constdata) for test
    inv_mpu.o(i.inv_orientation_matrix_to_scalar) refers to inv_mpu.o(i.inv_row_2_scale) for inv_row_2_scale
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_configure_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_reg) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu.o(i.mpu_get_accel_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_accel_sens) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_dmp_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_fifo_config) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_reg) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_get_gyro_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_gyro_sens) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_int_status) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_get_int_status) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_lpf) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_power_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_sample_rate) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_get_temperature) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu.o(i.mpu_get_temperature) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    inv_mpu.o(i.mpu_get_temperature) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.mpu_get_temperature) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    inv_mpu.o(i.mpu_get_temperature) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu.o(i.mpu_get_temperature) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    inv_mpu.o(i.mpu_get_temperature) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.mpu_get_temperature) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    inv_mpu.o(i.mpu_get_temperature) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_init) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_init) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_init) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_init) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu.o(i.mpu_load_firmware) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(i.mpu_load_firmware) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_load_firmware) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_lp_accel_mode) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_accel_mode) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_get_fifo_config) for mpu_get_fifo_config
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_lp_motion_interrupt) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_fifo) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(i.get_ms) for get_ms
    inv_mpu.o(i.mpu_read_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_fifo_stream) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_read_fifo_stream) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_mem) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_read_mem) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_read_mem) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_read_reg) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_read_reg) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_reg_dump) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_reg_dump) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_reset_fifo) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_reset_fifo) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_reset_fifo) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_dmp_state) for mpu_set_dmp_state
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_fsr) for mpu_get_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_lpf) for mpu_get_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_sample_rate) for mpu_get_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_get_fifo_config) for mpu_get_fifo_config
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.get_st_biases) for get_st_biases
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.accel_self_test) for accel_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.gyro_self_test) for gyro_self_test
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_gyro_fsr) for mpu_set_gyro_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_accel_fsr) for mpu_set_accel_fsr
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_set_sensors) for mpu_set_sensors
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(i.mpu_configure_fifo) for mpu_configure_fifo
    inv_mpu.o(i.mpu_run_self_test) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_accel_bias) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_set_accel_bias) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_accel_bias) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_accel_fsr) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_accel_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_bypass) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    inv_mpu.o(i.mpu_set_bypass) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_bypass) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_set_bypass) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.set_int_enable) for set_int_enable
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_bypass) for mpu_set_bypass
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_set_sample_rate) for mpu_set_sample_rate
    inv_mpu.o(i.mpu_set_dmp_state) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu.o(i.mpu_set_dmp_state) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_gyro_fsr) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_int_latched) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_int_latched) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_int_level) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_lpf) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_lpf) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_lp_accel_mode) for mpu_lp_accel_mode
    inv_mpu.o(i.mpu_set_sample_rate) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(i.mpu_set_lpf) for mpu_set_lpf
    inv_mpu.o(i.mpu_set_sample_rate) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_set_sensors) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(i.mpu_set_int_latched) for mpu_set_int_latched
    inv_mpu.o(i.mpu_set_sensors) refers to delay.o(i.delay_ms) for delay_ms
    inv_mpu.o(i.mpu_set_sensors) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.mpu_write_mem) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.mpu_write_mem) refers to inv_mpu.o(.data) for st
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_run_self_test) for mpu_run_self_test
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_gyro_sens) for mpu_get_gyro_sens
    inv_mpu.o(i.run_self_test) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    inv_mpu.o(i.run_self_test) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu.o(i.run_self_test) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    inv_mpu.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) for dmp_set_gyro_bias
    inv_mpu.o(i.run_self_test) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu.o(i.run_self_test) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) for dmp_set_accel_bias
    inv_mpu.o(i.set_int_enable) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    inv_mpu.o(i.set_int_enable) refers to inv_mpu.o(.data) for st
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for reg
    inv_mpu_dmp_motion_driver.o(i.decode_gesture) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to aeabi_memset.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) for dmp_enable_gyro_cal
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) for dmp_set_tap_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) for dmp_set_tap_axes
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) for dmp_set_tap_count
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) for dmp_set_tap_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) for dmp_set_tap_time_multi
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) for dmp_set_shake_reject_thresh
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) for dmp_set_shake_reject_time
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) for dmp_set_shake_reject_timeout
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) for dmp_enable_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat) for dmp_enable_6x_lp_quat
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to aeabi_memset.o(.text) for __aeabi_memset
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time) refers to inv_mpu.o(i.mpu_read_mem) for mpu_read_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu.o(i.mpu_load_firmware) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware) refers to inv_mpu_dmp_motion_driver.o(.constdata) for dmp_memory
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_read_fifo_stream) for mpu_read_fifo_stream
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu.o(i.mpu_reset_fifo) for mpu_reset_fifo
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(i.decode_gesture) for decode_gesture
    inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_get_accel_sens) for mpu_get_accel_sens
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation) refers to inv_mpu_dmp_motion_driver.o(.bss) for dmp
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_get_accel_fsr) for mpu_get_accel_fsr
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi) refers to inv_mpu.o(i.mpu_write_mem) for mpu_write_mem
    mpu_exti.o(i.EXTI15_10_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_GetITStatus) for EXTI_GetITStatus
    mpu_exti.o(i.EXTI15_10_IRQHandler) refers to inv_mpu.o(i.MPU6050_DMP_Get_Data) for MPU6050_DMP_Get_Data
    mpu_exti.o(i.EXTI15_10_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    mpu_exti.o(i.EXTI15_10_IRQHandler) refers to mpu_exti.o(.data) for Yaw
    mpu_exti.o(i.MPU_EXTI_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    mpu_exti.o(i.MPU_EXTI_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    mpu_exti.o(i.MPU_EXTI_Init) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    mpu_exti.o(i.MPU_EXTI_Init) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    mpu_exti.o(i.MPU_EXTI_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    mpu_exti.o(i.MPU_EXTI_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    mpu6050.o(i.MPU6050_Init) refers to mpu6050_i2c.o(i.MPU6050_IIC_IO_Init) for MPU6050_IIC_IO_Init
    mpu6050.o(i.MPU6050_Init) refers to mpu6050.o(i.mpu6050_write_reg) for mpu6050_write_reg
    mpu6050.o(i.mpu6050_read) refers to mpu6050_i2c.o(i.MPU6050_IIC_Start) for MPU6050_IIC_Start
    mpu6050.o(i.mpu6050_read) refers to mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte) for MPU6050_IIC_Send_Byte
    mpu6050.o(i.mpu6050_read) refers to mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte) for MPU6050_IIC_Read_Byte
    mpu6050.o(i.mpu6050_read) refers to mpu6050_i2c.o(i.MPU6050_IIC_Stop) for MPU6050_IIC_Stop
    mpu6050.o(i.mpu6050_read_reg) refers to mpu6050.o(i.mpu6050_read) for mpu6050_read
    mpu6050.o(i.mpu6050_write) refers to mpu6050_i2c.o(i.MPU6050_IIC_Start) for MPU6050_IIC_Start
    mpu6050.o(i.mpu6050_write) refers to mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte) for MPU6050_IIC_Send_Byte
    mpu6050.o(i.mpu6050_write) refers to mpu6050_i2c.o(i.MPU6050_IIC_Stop) for MPU6050_IIC_Stop
    mpu6050.o(i.mpu6050_write_reg) refers to mpu6050.o(i.mpu6050_write) for mpu6050_write
    mpu6050_i2c.o(i.MPU6050_IIC_IO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    mpu6050_i2c.o(i.MPU6050_IIC_IO_Init) refers to sys.o(i.My_GPIO_Init) for My_GPIO_Init
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN) for MPU6050_IIC_SDA_IO_IN
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack) refers to delay.o(i.delay_us) for delay_us
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack) refers to mpu6050_i2c.o(i.MPU6050_IIC_Stop) for MPU6050_IIC_Stop
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN) for MPU6050_IIC_SDA_IO_IN
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte) refers to delay.o(i.delay_us) for delay_us
    mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte) refers to mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack) for MPU6050_IIC_Send_Ack
    mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN) refers to sys.o(i.My_GPIO_Init) for My_GPIO_Init
    mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT) refers to sys.o(i.My_GPIO_Init) for My_GPIO_Init
    mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT) for MPU6050_IIC_SDA_IO_OUT
    mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack) refers to delay.o(i.delay_us) for delay_us
    mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT) for MPU6050_IIC_SDA_IO_OUT
    mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte) refers to delay.o(i.delay_us) for delay_us
    mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte) refers to mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack) for MPU6050_IIC_Read_Ack
    mpu6050_i2c.o(i.MPU6050_IIC_Start) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT) for MPU6050_IIC_SDA_IO_OUT
    mpu6050_i2c.o(i.MPU6050_IIC_Start) refers to delay.o(i.delay_us) for delay_us
    mpu6050_i2c.o(i.MPU6050_IIC_Stop) refers to mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT) for MPU6050_IIC_SDA_IO_OUT
    mpu6050_i2c.o(i.MPU6050_IIC_Stop) refers to delay.o(i.delay_us) for delay_us
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(i.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to fleqf.o(x$fpl$fleqf) for __aeabi_cfcmple
    oled.o(i.OLED_DrawEllipse) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled.o(i.OLED_DrawEllipse) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(i.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(i.OLED_DrawEllipse) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_pnpoly) for OLED_pnpoly
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(i.OLED_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_GPIO_Init) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_SendByte) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Start) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SDA) for OLED_W_SDA
    oled.o(i.OLED_I2C_Stop) refers to oled.o(i.OLED_W_SCL) for OLED_W_SCL
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_GPIO_Init) for OLED_GPIO_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(i.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(i.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(i.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_data.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowFloatNum) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(i.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(i.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(i.OLED_ClearArea) for OLED_ClearArea
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to strcmpv7m.o(.text) for strcmp
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowString) refers to oled_data.o(.constdata) for OLED_CF16x16
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Update) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for OLED_DisplayBuf
    oled.o(i.OLED_W_SCL) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_W_SDA) refers to stm32f10x_gpio.o(i.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteCommand) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Start) for OLED_I2C_Start
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(i.OLED_WriteData) refers to oled.o(i.OLED_I2C_Stop) for OLED_I2C_Stop
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(.data) for fac_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    sys.o(i.My_GPIO_Init) refers to sys.o(i.power) for power
    sys.o(i.My_GPIO_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    sys.o(i.My_GPIO_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    sys.o(i.log_2) refers to sys.o(i.power) for power
    main.o(i.TIM4_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    main.o(i.TIM4_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    main.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    main.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    main.o(i.USART1_IRQHandler) refers to car.o(i.Car_Stop) for Car_Stop
    main.o(i.USART1_IRQHandler) refers to car.o(i.Go_Ahead) for Go_Ahead
    main.o(i.USART1_IRQHandler) refers to car.o(i.Go_Back) for Go_Back
    main.o(i.USART1_IRQHandler) refers to car.o(i.Turn_Left) for Turn_Left
    main.o(i.USART1_IRQHandler) refers to car.o(i.Turn_Right) for Turn_Right
    main.o(i.USART1_IRQHandler) refers to car.o(i.Self_Left) for Self_Left
    main.o(i.USART1_IRQHandler) refers to car.o(i.Self_Right) for Self_Right
    main.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ClearITPendingBit) for USART_ClearITPendingBit
    main.o(i.USART1_IRQHandler) refers to main.o(.data) for Data1
    main.o(i.main) refers to car.o(i.Car_Init) for Car_Init
    main.o(i.main) refers to serial.o(i.Serial_Init) for Serial_Init
    main.o(i.main) refers to track.o(i.Infrared_Init) for Infrared_Init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to timer.o(i.Timer1_Init) for Timer1_Init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to mpu_exti.o(i.MPU_EXTI_Init) for MPU_EXTI_Init
    main.o(i.main) refers to inv_mpu.o(i.MPU6050_DMP_Init) for MPU6050_DMP_Init
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to oled.o(i.OLED_Update) for OLED_Update
    llsdiv.o(.text) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    aeabi_memset.o(.text) refers to rt_memclr.o(.text) for _memset
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    asin.o(i.__softfp_asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.asin) for asin
    asin.o(i.asin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.asin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    asin.o(i.asin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    asin.o(i.asin) refers to _rserrno.o(.text) for __set_errno
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.asin) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    asin.o(i.asin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    asin.o(i.asin) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    asin.o(i.asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.asin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    asin.o(i.asin) refers to asin.o(.constdata) for .constdata
    asin.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.__asin$lsc) for __asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.__asin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    asin_x.o(i.__asin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    asin_x.o(i.__asin$lsc) refers to _rserrno.o(.text) for __set_errno
    asin_x.o(i.__asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.__asin$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    asin_x.o(i.__asin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    asin_x.o(i.__asin$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    asin_x.o(i.__asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.__asin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    asin_x.o(i.__asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.__atan2$lsc) for __atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.__atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.__atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.__atan2$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2_x.o(i.__atan2$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2_x.o(i.__atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.__atan$lsc) for __atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan_x.o(i.__atan$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan_x.o(i.__atan$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan_x.o(i.__atan$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan_x.o(i.__atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.__atan$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan_x.o(i.__atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dsqrt_noumaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_noumaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000003) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B) for __rt_lib_shutdown_user_alloc_1
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_Cmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DMACmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_DeInit), (92 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_Init), (80 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_RegularChannelConfig), (184 bytes).
    Removing stm32f10x_adc.o(i.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearFlag), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_bkp.o(i.BKP_DeInit), (16 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetFlagStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_GetITStatus), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_RTCOutputConfig), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_ReadBackupRegister), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_SetRTCCalibrationValue), (28 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(i.BKP_WriteBackupRegister), (28 bytes).
    Removing stm32f10x_can.o(i.CAN_CancelTransmit), (48 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearFlag), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_ClearITPendingBit), (168 bytes).
    Removing stm32f10x_can.o(i.CAN_DBGFreeze), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f10x_can.o(i.CAN_FIFORelease), (22 bytes).
    Removing stm32f10x_can.o(i.CAN_FilterInit), (264 bytes).
    Removing stm32f10x_can.o(i.CAN_GetFlagStatus), (120 bytes).
    Removing stm32f10x_can.o(i.CAN_GetITStatus), (288 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLSBTransmitErrorCounter), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetLastErrorCode), (12 bytes).
    Removing stm32f10x_can.o(i.CAN_GetReceiveErrorCounter), (10 bytes).
    Removing stm32f10x_can.o(i.CAN_ITConfig), (18 bytes).
    Removing stm32f10x_can.o(i.CAN_Init), (276 bytes).
    Removing stm32f10x_can.o(i.CAN_MessagePending), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_OperatingModeRequest), (162 bytes).
    Removing stm32f10x_can.o(i.CAN_Receive), (240 bytes).
    Removing stm32f10x_can.o(i.CAN_SlaveStartBank), (52 bytes).
    Removing stm32f10x_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f10x_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f10x_can.o(i.CAN_TTComModeCmd), (118 bytes).
    Removing stm32f10x_can.o(i.CAN_Transmit), (294 bytes).
    Removing stm32f10x_can.o(i.CAN_TransmitStatus), (160 bytes).
    Removing stm32f10x_can.o(i.CAN_WakeUp), (48 bytes).
    Removing stm32f10x_can.o(i.CheckITStatus), (18 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearFlag), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_ClearITPendingBit), (36 bytes).
    Removing stm32f10x_cec.o(i.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_DeInit), (22 bytes).
    Removing stm32f10x_cec.o(i.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetFlagStatus), (48 bytes).
    Removing stm32f10x_cec.o(i.CEC_GetITStatus), (40 bytes).
    Removing stm32f10x_cec.o(i.CEC_ITConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_Init), (32 bytes).
    Removing stm32f10x_cec.o(i.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_ReceiveDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(i.CEC_StartOfMessage), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcBlockCRC), (36 bytes).
    Removing stm32f10x_crc.o(i.CRC_CalcCRC), (16 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f10x_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_Cmd), (40 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_Config), (32 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearITPendingBit), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_Cmd), (24 bytes).
    Removing stm32f10x_dma.o(i.DMA_DeInit), (332 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetITStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_ITConfig), (18 bytes).
    Removing stm32f10x_dma.o(i.DMA_Init), (60 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EnableWriteProtection), (196 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllBank1Pages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseAllPages), (72 bytes).
    Removing stm32f10x_flash.o(i.FLASH_EraseOptionBytes), (168 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ErasePage), (76 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetBank1Status), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetFlagStatus), (48 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetPrefetchBufferStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetReadOutProtectionStatus), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetStatus), (52 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetUserOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(i.FLASH_HalfCycleAccessCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ITConfig), (32 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Lock), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_LockBank1), (20 bytes).
    Removing stm32f10x_flash.o(i.FLASH_PrefetchBufferCmd), (28 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramHalfWord), (64 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramOptionByteData), (84 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ProgramWord), (108 bytes).
    Removing stm32f10x_flash.o(i.FLASH_ReadOutProtection), (172 bytes).
    Removing stm32f10x_flash.o(i.FLASH_SetLatency), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_Unlock), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UnlockBank1), (24 bytes).
    Removing stm32f10x_flash.o(i.FLASH_UserOptionByteConfig), (104 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastBank1Operation), (38 bytes).
    Removing stm32f10x_flash.o(i.FLASH_WaitForLastOperation), (38 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd), (52 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMInit), (230 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_CheckEvent), (42 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearFlag), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DeInit), (56 bytes).
    Removing stm32f10x_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_FastModeDutyCycleConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetFlagStatus), (58 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetITStatus), (38 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetLastEvent), (26 bytes).
    Removing stm32f10x_i2c.o(i.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ITConfig), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Init), (236 bytes).
    Removing stm32f10x_i2c.o(i.I2C_NACKPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_OwnAddress2Config), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_PECPositionConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SMBusAlertConfig), (28 bytes).
    Removing stm32f10x_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(i.I2C_SoftwareResetCmd), (22 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f10x_i2c.o(i.I2C_StructInit), (30 bytes).
    Removing stm32f10x_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_GetFlagStatus), (24 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_ClearFlag), (20 bytes).
    Removing stm32f10x_pwr.o(i.PWR_DeInit), (22 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTANDBYMode), (52 bytes).
    Removing stm32f10x_pwr.o(i.PWR_EnterSTOPMode), (64 bytes).
    Removing stm32f10x_pwr.o(i.PWR_GetFlagStatus), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f10x_pwr.o(i.PWR_PVDLevelConfig), (24 bytes).
    Removing stm32f10x_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ADCCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearFlag), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_rtc.o(i.RTC_EnterConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ExitConfigMode), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetCounter), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetDivider), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetFlagStatus), (24 bytes).
    Removing stm32f10x_rtc.o(i.RTC_GetITStatus), (36 bytes).
    Removing stm32f10x_rtc.o(i.RTC_ITConfig), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetAlarm), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetCounter), (28 bytes).
    Removing stm32f10x_rtc.o(i.RTC_SetPrescaler), (32 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForLastTask), (20 bytes).
    Removing stm32f10x_rtc.o(i.RTC_WaitForSynchro), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_DeInit), (36 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetFlagStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetITStatus), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_GetResponse), (24 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ITConfig), (32 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_Init), (48 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendCommand), (44 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetPowerState), (28 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.I2S_Init), (232 bytes).
    Removing stm32f10x_spi.o(i.I2S_StructInit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_BiDirectionalLineConfig), (28 bytes).
    Removing stm32f10x_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_DataSizeConfig), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRC), (16 bytes).
    Removing stm32f10x_spi.o(i.SPI_GetCRCPolynomial), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ClearITPendingBit), (20 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DMACmd), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_DeInit), (88 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetFlagStatus), (18 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_GetITStatus), (52 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ITConfig), (32 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_ReceiveData), (6 bytes).
    Removing stm32f10x_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(i.SPI_Init), (60 bytes).
    Removing stm32f10x_spi.o(i.SPI_NSSInternalSoftwareConfig), (30 bytes).
    Removing stm32f10x_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f10x_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CtrlPWMOutputs), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2Init), (164 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOutputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_DeInit), (22 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_GetFlagStatus), (12 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetPrescaler), (24 bytes).
    Removing stm32f10x_wwdg.o(i.WWDG_SetWindowValue), (40 bytes).
    Removing serial.o(i.Serial_Pow), (20 bytes).
    Removing serial.o(i.Serial_Printf), (36 bytes).
    Removing serial.o(i.Serial_SendArray), (26 bytes).
    Removing serial.o(i.Serial_SendByte), (32 bytes).
    Removing serial.o(i.Serial_SendNumber), (58 bytes).
    Removing serial.o(i.Serial_SendString), (26 bytes).
    Removing serial.o(i.fputc), (16 bytes).
    Removing track.o(i.track_start), (64 bytes).
    Removing inv_mpu.o(i.get_ms), (2 bytes).
    Removing inv_mpu.o(i.mpu_get_accel_reg), (116 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_fsr), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_reg), (8 bytes).
    Removing inv_mpu.o(i.mpu_get_compass_sample_rate), (10 bytes).
    Removing inv_mpu.o(i.mpu_get_dmp_state), (20 bytes).
    Removing inv_mpu.o(i.mpu_get_gyro_reg), (108 bytes).
    Removing inv_mpu.o(i.mpu_get_int_status), (68 bytes).
    Removing inv_mpu.o(i.mpu_get_power_state), (28 bytes).
    Removing inv_mpu.o(i.mpu_get_temperature), (160 bytes).
    Removing inv_mpu.o(i.mpu_lp_motion_interrupt), (516 bytes).
    Removing inv_mpu.o(i.mpu_read_fifo), (504 bytes).
    Removing inv_mpu.o(i.mpu_read_reg), (72 bytes).
    Removing inv_mpu.o(i.mpu_reg_dump), (76 bytes).
    Removing inv_mpu.o(i.mpu_set_accel_bias), (384 bytes).
    Removing inv_mpu.o(i.mpu_set_compass_sample_rate), (8 bytes).
    Removing inv_mpu.o(i.mpu_set_int_level), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_enabled_features), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_fifo_rate), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_step_count), (66 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_get_pedometer_walk_time), (72 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_android_orient_cb), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_register_tap_cb), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_interrupt_mode), (92 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_step_count), (42 bytes).
    Removing inv_mpu_dmp_motion_driver.o(i.dmp_set_pedometer_walk_time), (48 bytes).
    Removing mpu6050.o(i.MPU6050_Init), (48 bytes).
    Removing mpu6050.o(i.mpu6050_read_reg), (22 bytes).
    Removing mpu6050.o(i.mpu6050_write_reg), (18 bytes).
    Removing oled.o(i.OLED_DrawArc), (618 bytes).
    Removing oled.o(i.OLED_DrawCircle), (352 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (812 bytes).
    Removing oled.o(i.OLED_DrawLine), (374 bytes).
    Removing oled.o(i.OLED_DrawPoint), (80 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (142 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (232 bytes).
    Removing oled.o(i.OLED_GetPoint), (68 bytes).
    Removing oled.o(i.OLED_IsInAngle), (124 bytes).
    Removing oled.o(i.OLED_Pow), (20 bytes).
    Removing oled.o(i.OLED_Printf), (50 bytes).
    Removing oled.o(i.OLED_Reverse), (52 bytes).
    Removing oled.o(i.OLED_ReverseArea), (144 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (70 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (210 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (96 bytes).
    Removing oled.o(i.OLED_ShowNum), (76 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (112 bytes).
    Removing oled.o(i.OLED_UpdateArea), (124 bytes).
    Removing oled.o(i.OLED_pnpoly), (140 bytes).
    Removing test.o(i.test1), (2 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing sys.o(i.log_2), (30 bytes).

505 unused section(s) (total 24148 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llsdiv.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  aeabi_memset.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_noumaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/feqf.s                          0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    Hardware\Car.c                           0x00000000   Number         0  car.o ABSOLUTE
    Hardware\Encoder.c                       0x00000000   Number         0  encoder.o ABSOLUTE
    Hardware\MPU6050.c                       0x00000000   Number         0  mpu6050.o ABSOLUTE
    Hardware\MPU6050_I2C.c                   0x00000000   Number         0  mpu6050_i2c.o ABSOLUTE
    Hardware\MPU_EXTI.c                      0x00000000   Number         0  mpu_exti.o ABSOLUTE
    Hardware\Motor.c                         0x00000000   Number         0  motor.o ABSOLUTE
    Hardware\OLED.c                          0x00000000   Number         0  oled.o ABSOLUTE
    Hardware\OLED_Data.c                     0x00000000   Number         0  oled_data.o ABSOLUTE
    Hardware\PWM.c                           0x00000000   Number         0  pwm.o ABSOLUTE
    Hardware\Serial.c                        0x00000000   Number         0  serial.o ABSOLUTE
    Hardware\Timer.c                         0x00000000   Number         0  timer.o ABSOLUTE
    Hardware\Track.c                         0x00000000   Number         0  track.o ABSOLUTE
    Hardware\Ultrasound.c                    0x00000000   Number         0  ultrasound.o ABSOLUTE
    Hardware\inv_mpu.c                       0x00000000   Number         0  inv_mpu.o ABSOLUTE
    Hardware\inv_mpu_dmp_motion_driver.c     0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    Hardware\key.c                           0x00000000   Number         0  key_1.o ABSOLUTE
    Hardware\test.c                          0x00000000   Number         0  test.o ABSOLUTE
    Library\misc.c                           0x00000000   Number         0  misc.o ABSOLUTE
    Library\stm32f10x_adc.c                  0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    Library\stm32f10x_bkp.c                  0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    Library\stm32f10x_can.c                  0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    Library\stm32f10x_cec.c                  0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    Library\stm32f10x_crc.c                  0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    Library\stm32f10x_dac.c                  0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    Library\stm32f10x_dbgmcu.c               0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    Library\stm32f10x_dma.c                  0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    Library\stm32f10x_exti.c                 0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    Library\stm32f10x_flash.c                0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    Library\stm32f10x_fsmc.c                 0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    Library\stm32f10x_gpio.c                 0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    Library\stm32f10x_i2c.c                  0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    Library\stm32f10x_iwdg.c                 0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    Library\stm32f10x_pwr.c                  0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    Library\stm32f10x_rcc.c                  0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    Library\stm32f10x_rtc.c                  0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    Library\stm32f10x_sdio.c                 0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    Library\stm32f10x_spi.c                  0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    Library\stm32f10x_tim.c                  0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    Library\stm32f10x_usart.c                0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    Library\stm32f10x_wwdg.c                 0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    Start\\core_cm3.c                        0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\core_cm3.c                         0x00000000   Number         0  core_cm3.o ABSOLUTE
    Start\startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    Start\system_stm32f10x.c                 0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    System\Delay.c                           0x00000000   Number         0  delay.o ABSOLUTE
    System\\sys.c                            0x00000000   Number         0  sys.o ABSOLUTE
    System\sys.c                             0x00000000   Number         0  sys.o ABSOLUTE
    User\main.c                              0x00000000   Number         0  main.o ABSOLUTE
    User\stm32f10x_it.c                      0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000128   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000144   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000160   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000162   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000162   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000164   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000003      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    .ARM.Collect$$libshutdown$$00000006      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000B      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    .ARM.Collect$$libshutdown$$0000000E      0x08000166   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$0000000F      0x08000166   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$rtentry$$00000000          0x08000168   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000168   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000168   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800016e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800016e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000172   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000172   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800017a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800017c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800017c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000180   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000188   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x080001c8   Section       72  llsdiv.o(.text)
    .text                                    0x08000210   Section        0  memcmp.o(.text)
    .text                                    0x08000268   Section       16  aeabi_memset.o(.text)
    .text                                    0x08000278   Section      128  strcmpv7m.o(.text)
    .text                                    0x080002f8   Section        0  heapauxi.o(.text)
    .text                                    0x080002fe   Section      238  lludivv7m.o(.text)
    .text                                    0x080003ec   Section        0  _rserrno.o(.text)
    .text                                    0x08000402   Section       68  rt_memclr.o(.text)
    .text                                    0x08000448   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000450   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080004a0   Section        8  libspace.o(.text)
    .text                                    0x080004a8   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080004f2   Section        0  exit.o(.text)
    .text                                    0x08000500   Section        0  sys_exit.o(.text)
    .text                                    0x0800050c   Section        2  use_no_semi.o(.text)
    .text                                    0x0800050e   Section        0  indicate_semi.o(.text)
    i.BusFault_Handler                       0x0800050e   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.Car_Init                               0x08000512   Section        0  car.o(i.Car_Init)
    i.Car_Stop                               0x0800051a   Section        0  car.o(i.Car_Stop)
    i.DebugMon_Handler                       0x0800052a   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.EXTI15_10_IRQHandler                   0x0800052c   Section        0  mpu_exti.o(i.EXTI15_10_IRQHandler)
    i.EXTI_ClearITPendingBit                 0x0800055c   Section        0  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_GetITStatus                       0x08000568   Section        0  stm32f10x_exti.o(i.EXTI_GetITStatus)
    i.EXTI_Init                              0x08000590   Section        0  stm32f10x_exti.o(i.EXTI_Init)
    i.GPIO_EXTILineConfig                    0x08000624   Section        0  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    i.GPIO_Init                              0x08000664   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ResetBits                         0x0800077a   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x0800077e   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GPIO_WriteBit                          0x08000782   Section        0  stm32f10x_gpio.o(i.GPIO_WriteBit)
    i.Go_Ahead                               0x0800078c   Section        0  car.o(i.Go_Ahead)
    i.Go_Back                                0x0800079e   Section        0  car.o(i.Go_Back)
    i.HardFault_Handler                      0x080007b0   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.Infrared_Init                          0x080007b4   Section        0  track.o(i.Infrared_Init)
    i.MPU6050_DMP_Get_Data                   0x080007e0   Section        0  inv_mpu.o(i.MPU6050_DMP_Get_Data)
    i.MPU6050_DMP_Init                       0x08000a04   Section        0  inv_mpu.o(i.MPU6050_DMP_Init)
    i.MPU6050_IIC_IO_Init                    0x08000a54   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_IO_Init)
    i.MPU6050_IIC_Read_Ack                   0x08000a88   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack)
    i.MPU6050_IIC_Read_Byte                  0x08000ad8   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte)
    i.MPU6050_IIC_SDA_IO_IN                  0x08000b38   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN)
    i.MPU6050_IIC_SDA_IO_OUT                 0x08000b54   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT)
    i.MPU6050_IIC_Send_Ack                   0x08000b6c   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack)
    i.MPU6050_IIC_Send_Byte                  0x08000ba4   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte)
    i.MPU6050_IIC_Start                      0x08000c08   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Start)
    i.MPU6050_IIC_Stop                       0x08000c40   Section        0  mpu6050_i2c.o(i.MPU6050_IIC_Stop)
    i.MPU_EXTI_Init                          0x08000c7c   Section        0  mpu_exti.o(i.MPU_EXTI_Init)
    i.MemManage_Handler                      0x08000cfc   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.Motor_Init                             0x08000d00   Section        0  motor.o(i.Motor_Init)
    i.Motor_SetLeftSpeed                     0x08000d30   Section        0  motor.o(i.Motor_SetLeftSpeed)
    i.Motor_SetRightSpeed                    0x08000d88   Section        0  motor.o(i.Motor_SetRightSpeed)
    i.My_GPIO_Init                           0x08000de0   Section        0  sys.o(i.My_GPIO_Init)
    i.NMI_Handler                            0x08000e90   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08000e94   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08000f04   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.OLED_Clear                             0x08000f18   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ClearArea                         0x08000f40   Section        0  oled.o(i.OLED_ClearArea)
    i.OLED_GPIO_Init                         0x08000fd0   Section        0  oled.o(i.OLED_GPIO_Init)
    i.OLED_I2C_SendByte                      0x08001030   Section        0  oled.o(i.OLED_I2C_SendByte)
    i.OLED_I2C_Start                         0x0800106e   Section        0  oled.o(i.OLED_I2C_Start)
    i.OLED_I2C_Stop                          0x0800108a   Section        0  oled.o(i.OLED_I2C_Stop)
    i.OLED_Init                              0x080010a0   Section        0  oled.o(i.OLED_Init)
    i.OLED_SetCursor                         0x0800113a   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x0800115c   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x080011b0   Section        0  oled.o(i.OLED_ShowImage)
    i.OLED_ShowString                        0x080012ac   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x08001470   Section        0  oled.o(i.OLED_Update)
    i.OLED_W_SCL                             0x08001498   Section        0  oled.o(i.OLED_W_SCL)
    i.OLED_W_SDA                             0x080014b0   Section        0  oled.o(i.OLED_W_SDA)
    i.OLED_WriteCommand                      0x080014c8   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x080014e8   Section        0  oled.o(i.OLED_WriteData)
    i.PWM_Init                               0x08001518   Section        0  pwm.o(i.PWM_Init)
    i.PWM_SetCompare3                        0x080015b8   Section        0  pwm.o(i.PWM_SetCompare3)
    i.PWM_SetCompare4                        0x080015c8   Section        0  pwm.o(i.PWM_SetCompare4)
    i.PendSV_Handler                         0x080015d8   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_APB1PeriphClockCmd                 0x080015dc   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x080015fc   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_GetClocksFreq                      0x0800161c   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x080016f0   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.Self_Left                              0x080016f2   Section        0  car.o(i.Self_Left)
    i.Self_Right                             0x08001706   Section        0  car.o(i.Self_Right)
    i.Serial_Init                            0x08001718   Section        0  serial.o(i.Serial_Init)
    i.SetSysClock                            0x080017d0   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x080017d1   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x080017d8   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x080017d9   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_CLKSourceConfig                0x080018b8   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x080018e0   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x080018e4   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM4_IRQHandler                        0x08001944   Section        0  main.o(i.TIM4_IRQHandler)
    i.TIM_ClearFlag                          0x08001960   Section        0  stm32f10x_tim.o(i.TIM_ClearFlag)
    i.TIM_ClearITPendingBit                  0x08001966   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x0800196c   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_GetITStatus                        0x08001984   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x080019a6   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_InternalClockConfig                0x080019b8   Section        0  stm32f10x_tim.o(i.TIM_InternalClockConfig)
    i.TIM_OC3Init                            0x080019c4   Section        0  stm32f10x_tim.o(i.TIM_OC3Init)
    i.TIM_OC4Init                            0x08001a64   Section        0  stm32f10x_tim.o(i.TIM_OC4Init)
    i.TIM_OCStructInit                       0x08001ae0   Section        0  stm32f10x_tim.o(i.TIM_OCStructInit)
    i.TIM_SetCompare3                        0x08001af4   Section        0  stm32f10x_tim.o(i.TIM_SetCompare3)
    i.TIM_SetCompare4                        0x08001af8   Section        0  stm32f10x_tim.o(i.TIM_SetCompare4)
    i.TIM_TimeBaseInit                       0x08001b00   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.Timer1_Init                            0x08001ba4   Section        0  timer.o(i.Timer1_Init)
    i.Turn_Left                              0x08001c24   Section        0  car.o(i.Turn_Left)
    i.Turn_Right                             0x08001c36   Section        0  car.o(i.Turn_Right)
    i.USART1_IRQHandler                      0x08001c48   Section        0  main.o(i.USART1_IRQHandler)
    i.USART_ClearITPendingBit                0x08001ccc   Section        0  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    i.USART_Cmd                              0x08001cea   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08001d02   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08001d56   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08001da0   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08001e78   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x08001e82   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08001e86   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__kernel_poly                          0x08001eae   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_infnan                   0x08001f58   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x08001f5e   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x08001f62   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x08001f70   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.accel_self_test                        0x08001f80   Section        0  inv_mpu.o(i.accel_self_test)
    accel_self_test                          0x08001f81   Thumb Code   164  inv_mpu.o(i.accel_self_test)
    i.asin                                   0x08002028   Section        0  asin.o(i.asin)
    i.atan                                   0x08002298   Section        0  atan.o(i.atan)
    i.atan2                                  0x080024b8   Section        0  atan2.o(i.atan2)
    i.decode_gesture                         0x08002638   Section        0  inv_mpu_dmp_motion_driver.o(i.decode_gesture)
    decode_gesture                           0x08002639   Thumb Code    94  inv_mpu_dmp_motion_driver.o(i.decode_gesture)
    i.delay_init                             0x0800269c   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x080026e0   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x0800272c   Section        0  delay.o(i.delay_us)
    i.dmp_enable_6x_lp_quat                  0x08002778   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat)
    i.dmp_enable_feature                     0x080027b4   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature)
    i.dmp_enable_gyro_cal                    0x080029d0   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal)
    i.dmp_enable_lp_quat                     0x08002a28   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat)
    i.dmp_load_motion_driver_firmware        0x08002a64   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware)
    i.dmp_read_fifo                          0x08002a7c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo)
    i.dmp_set_accel_bias                     0x08002c44   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias)
    i.dmp_set_fifo_rate                      0x08002d74   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate)
    i.dmp_set_gyro_bias                      0x08002de4   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias)
    i.dmp_set_orientation                    0x08002f14   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation)
    i.dmp_set_shake_reject_thresh            0x0800304c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh)
    i.dmp_set_shake_reject_time              0x08003084   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time)
    i.dmp_set_shake_reject_timeout           0x080030aa   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout)
    i.dmp_set_tap_axes                       0x080030d0   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes)
    i.dmp_set_tap_count                      0x08003116   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count)
    i.dmp_set_tap_thresh                     0x0800313c   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh)
    i.dmp_set_tap_time                       0x080032dc   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time)
    i.dmp_set_tap_time_multi                 0x08003302   Section        0  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi)
    i.get_accel_prod_shift                   0x08003328   Section        0  inv_mpu.o(i.get_accel_prod_shift)
    get_accel_prod_shift                     0x08003329   Thumb Code   168  inv_mpu.o(i.get_accel_prod_shift)
    i.get_st_biases                          0x080033dc   Section        0  inv_mpu.o(i.get_st_biases)
    get_st_biases                            0x080033dd   Thumb Code  1132  inv_mpu.o(i.get_st_biases)
    i.gyro_self_test                         0x0800384c   Section        0  inv_mpu.o(i.gyro_self_test)
    gyro_self_test                           0x0800384d   Thumb Code   256  inv_mpu.o(i.gyro_self_test)
    i.inv_orientation_matrix_to_scalar       0x0800395c   Section        0  inv_mpu.o(i.inv_orientation_matrix_to_scalar)
    i.inv_row_2_scale                        0x08003984   Section        0  inv_mpu.o(i.inv_row_2_scale)
    i.main                                   0x080039d4   Section        0  main.o(i.main)
    i.mpu6050_read                           0x08003a0c   Section        0  mpu6050.o(i.mpu6050_read)
    i.mpu6050_write                          0x08003a68   Section        0  mpu6050.o(i.mpu6050_write)
    i.mpu_configure_fifo                     0x08003aac   Section        0  inv_mpu.o(i.mpu_configure_fifo)
    i.mpu_get_accel_fsr                      0x08003b1c   Section        0  inv_mpu.o(i.mpu_get_accel_fsr)
    i.mpu_get_accel_sens                     0x08003b68   Section        0  inv_mpu.o(i.mpu_get_accel_sens)
    i.mpu_get_fifo_config                    0x08003bbc   Section        0  inv_mpu.o(i.mpu_get_fifo_config)
    i.mpu_get_gyro_fsr                       0x08003bcc   Section        0  inv_mpu.o(i.mpu_get_gyro_fsr)
    i.mpu_get_gyro_sens                      0x08003c10   Section        0  inv_mpu.o(i.mpu_get_gyro_sens)
    i.mpu_get_lpf                            0x08003c60   Section        0  inv_mpu.o(i.mpu_get_lpf)
    i.mpu_get_sample_rate                    0x08003cb0   Section        0  inv_mpu.o(i.mpu_get_sample_rate)
    i.mpu_init                               0x08003cd0   Section        0  inv_mpu.o(i.mpu_init)
    i.mpu_load_firmware                      0x08003e60   Section        0  inv_mpu.o(i.mpu_load_firmware)
    i.mpu_lp_accel_mode                      0x08003f18   Section        0  inv_mpu.o(i.mpu_lp_accel_mode)
    i.mpu_read_fifo_stream                   0x08003ff8   Section        0  inv_mpu.o(i.mpu_read_fifo_stream)
    i.mpu_read_mem                           0x080040b8   Section        0  inv_mpu.o(i.mpu_read_mem)
    i.mpu_reset_fifo                         0x08004138   Section        0  inv_mpu.o(i.mpu_reset_fifo)
    i.mpu_run_self_test                      0x08004300   Section        0  inv_mpu.o(i.mpu_run_self_test)
    i.mpu_set_accel_fsr                      0x0800441c   Section        0  inv_mpu.o(i.mpu_set_accel_fsr)
    i.mpu_set_bypass                         0x080044a0   Section        0  inv_mpu.o(i.mpu_set_bypass)
    i.mpu_set_dmp_state                      0x080045ec   Section        0  inv_mpu.o(i.mpu_set_dmp_state)
    i.mpu_set_gyro_fsr                       0x0800467c   Section        0  inv_mpu.o(i.mpu_set_gyro_fsr)
    i.mpu_set_int_latched                    0x08004704   Section        0  inv_mpu.o(i.mpu_set_int_latched)
    i.mpu_set_lpf                            0x08004770   Section        0  inv_mpu.o(i.mpu_set_lpf)
    i.mpu_set_sample_rate                    0x080047f4   Section        0  inv_mpu.o(i.mpu_set_sample_rate)
    i.mpu_set_sensors                        0x08004890   Section        0  inv_mpu.o(i.mpu_set_sensors)
    i.mpu_write_mem                          0x08004960   Section        0  inv_mpu.o(i.mpu_write_mem)
    i.power                                  0x080049e0   Section        0  sys.o(i.power)
    i.run_self_test                          0x080049f4   Section        0  inv_mpu.o(i.run_self_test)
    i.set_int_enable                         0x08004a88   Section        0  inv_mpu.o(i.set_int_enable)
    set_int_enable                           0x08004a89   Thumb Code   138  inv_mpu.o(i.set_int_enable)
    i.sqrt                                   0x08004b18   Section        0  sqrt.o(i.sqrt)
    x$fpl$d2f                                0x08004b64   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08004bc8   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08004bd9   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08004d18   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$ddiv                               0x08004d28   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08004d2f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dmul                               0x08004fd8   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800512c   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x080051c8   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x080051d4   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x080051ec   Section      460  dsqrt_noumaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x080053b8   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x080053c9   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x0800558c   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x080055e4   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x080055f3   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmpinf                            0x080056a8   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fdiv                               0x080056c0   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x080056c1   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$feqf                               0x08005844   Section      104  feqf.o(x$fpl$feqf)
    x$fpl$ffix                               0x080058ac   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$ffixu                              0x080058e4   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x08005924   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x08005954   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fleqf                              0x0800597c   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x080059e4   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08005ae6   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08005b72   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x08005b7c   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$fsub                               0x08005be0   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08005bef   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$retnan                             0x08005cca   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x08005d2e   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x08005d8a   Section       48  trapv.o(x$fpl$trapveneer)
    x$fpl$usenofp                            0x08005dba   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x08005dbc   Section       80  inv_mpu.o(.constdata)
    .constdata                               0x08005e0c   Section     3062  inv_mpu_dmp_motion_driver.o(.constdata)
    dmp_memory                               0x08005e0c   Data        3062  inv_mpu_dmp_motion_driver.o(.constdata)
    .constdata                               0x08006a02   Section     2381  oled_data.o(.constdata)
    .constdata                               0x08007350   Section       80  asin.o(.constdata)
    pS                                       0x08007350   Data          48  asin.o(.constdata)
    qS                                       0x08007380   Data          32  asin.o(.constdata)
    .constdata                               0x080073a0   Section      152  atan.o(.constdata)
    atanhi                                   0x080073a0   Data          32  atan.o(.constdata)
    atanlo                                   0x080073c0   Data          32  atan.o(.constdata)
    aTodd                                    0x080073e0   Data          40  atan.o(.constdata)
    aTeven                                   0x08007408   Data          48  atan.o(.constdata)
    .constdata                               0x08007438   Section        8  qnan.o(.constdata)
    .data                                    0x20000000   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000014   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000014   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000024   Data           4  stm32f10x_rcc.o(.data)
    .data                                    0x20000028   Section       53  inv_mpu.o(.data)
    st                                       0x20000028   Data          44  inv_mpu.o(.data)
    gyro_orientation                         0x20000054   Data           9  inv_mpu.o(.data)
    .data                                    0x20000060   Section       12  mpu_exti.o(.data)
    .data                                    0x2000006c   Section        4  delay.o(.data)
    fac_us                                   0x2000006c   Data           1  delay.o(.data)
    fac_ms                                   0x2000006e   Data           2  delay.o(.data)
    .data                                    0x20000070   Section        6  main.o(.data)
    .bss                                     0x20000078   Section       16  inv_mpu_dmp_motion_driver.o(.bss)
    dmp                                      0x20000078   Data          16  inv_mpu_dmp_motion_driver.o(.bss)
    .bss                                     0x20000088   Section     1024  oled.o(.bss)
    .bss                                     0x20000488   Section       96  libspace.o(.bss)
    HEAP                                     0x200004e8   Section      512  startup_stm32f10x_md.o(HEAP)
    Heap_Mem                                 0x200004e8   Data         512  startup_stm32f10x_md.o(HEAP)
    STACK                                    0x200006e8   Section     1024  startup_stm32f10x_md.o(STACK)
    Stack_Mem                                0x200006e8   Data        1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x20000ae8   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000103   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000129   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000145   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000161   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_alloca_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_1                       0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_fp_trap_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_preinit_1                  0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x08000163   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000165   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_fp_trap_1              0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_heap_1                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_lib_shutdown_return                 0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_signal_1               0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_stdio_1                0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000003)
    __rt_lib_shutdown_user_alloc_1           0x08000167   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B)
    __rt_entry                               0x08000169   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000169   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000169   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800016f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000173   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800017b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800017d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000181   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000189   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI0_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI2_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM2_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM3_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001a3   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001a5   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __aeabi_ldivmod                          0x080001c9   Thumb Code     0  llsdiv.o(.text)
    _ll_sdiv                                 0x080001c9   Thumb Code    72  llsdiv.o(.text)
    memcmp                                   0x08000211   Thumb Code    88  memcmp.o(.text)
    __aeabi_memset                           0x08000269   Thumb Code    16  aeabi_memset.o(.text)
    strcmp                                   0x08000279   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x080002f9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002fb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002fd   Thumb Code     2  heapauxi.o(.text)
    __aeabi_uldivmod                         0x080002ff   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080002ff   Thumb Code   238  lludivv7m.o(.text)
    __read_errno                             0x080003ed   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080003f7   Thumb Code    12  _rserrno.o(.text)
    __aeabi_memclr                           0x08000403   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000403   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000407   Thumb Code     0  rt_memclr.o(.text)
    __aeabi_errno_addr                       0x08000449   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000449   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000449   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __aeabi_memclr4                          0x08000451   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000451   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000451   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000455   Thumb Code     0  rt_memclr_w.o(.text)
    __user_libspace                          0x080004a1   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080004a1   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080004a1   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080004a9   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080004f3   Thumb Code    12  exit.o(.text)
    _sys_exit                                0x08000501   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x0800050d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800050d   Thumb Code     2  use_no_semi.o(.text)
    BusFault_Handler                         0x0800050f   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    __semihosting_library_function           0x0800050f   Thumb Code     0  indicate_semi.o(.text)
    Car_Init                                 0x08000513   Thumb Code     8  car.o(i.Car_Init)
    Car_Stop                                 0x0800051b   Thumb Code    16  car.o(i.Car_Stop)
    DebugMon_Handler                         0x0800052b   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    EXTI15_10_IRQHandler                     0x0800052d   Thumb Code    34  mpu_exti.o(i.EXTI15_10_IRQHandler)
    EXTI_ClearITPendingBit                   0x0800055d   Thumb Code     6  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x08000569   Thumb Code    34  stm32f10x_exti.o(i.EXTI_GetITStatus)
    EXTI_Init                                0x08000591   Thumb Code   142  stm32f10x_exti.o(i.EXTI_Init)
    GPIO_EXTILineConfig                      0x08000625   Thumb Code    60  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    GPIO_Init                                0x08000665   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ResetBits                           0x0800077b   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x0800077f   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GPIO_WriteBit                            0x08000783   Thumb Code    10  stm32f10x_gpio.o(i.GPIO_WriteBit)
    Go_Ahead                                 0x0800078d   Thumb Code    18  car.o(i.Go_Ahead)
    Go_Back                                  0x0800079f   Thumb Code    18  car.o(i.Go_Back)
    HardFault_Handler                        0x080007b1   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    Infrared_Init                            0x080007b5   Thumb Code    40  track.o(i.Infrared_Init)
    MPU6050_DMP_Get_Data                     0x080007e1   Thumb Code   544  inv_mpu.o(i.MPU6050_DMP_Get_Data)
    MPU6050_DMP_Init                         0x08000a05   Thumb Code    74  inv_mpu.o(i.MPU6050_DMP_Init)
    MPU6050_IIC_IO_Init                      0x08000a55   Thumb Code    40  mpu6050_i2c.o(i.MPU6050_IIC_IO_Init)
    MPU6050_IIC_Read_Ack                     0x08000a89   Thumb Code    72  mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack)
    MPU6050_IIC_Read_Byte                    0x08000ad9   Thumb Code    86  mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte)
    MPU6050_IIC_SDA_IO_IN                    0x08000b39   Thumb Code    22  mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN)
    MPU6050_IIC_SDA_IO_OUT                   0x08000b55   Thumb Code    20  mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT)
    MPU6050_IIC_Send_Ack                     0x08000b6d   Thumb Code    48  mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack)
    MPU6050_IIC_Send_Byte                    0x08000ba5   Thumb Code    90  mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte)
    MPU6050_IIC_Start                        0x08000c09   Thumb Code    46  mpu6050_i2c.o(i.MPU6050_IIC_Start)
    MPU6050_IIC_Stop                         0x08000c41   Thumb Code    52  mpu6050_i2c.o(i.MPU6050_IIC_Stop)
    MPU_EXTI_Init                            0x08000c7d   Thumb Code   122  mpu_exti.o(i.MPU_EXTI_Init)
    MemManage_Handler                        0x08000cfd   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    Motor_Init                               0x08000d01   Thumb Code    42  motor.o(i.Motor_Init)
    Motor_SetLeftSpeed                       0x08000d31   Thumb Code    84  motor.o(i.Motor_SetLeftSpeed)
    Motor_SetRightSpeed                      0x08000d89   Thumb Code    84  motor.o(i.Motor_SetRightSpeed)
    My_GPIO_Init                             0x08000de1   Thumb Code   172  sys.o(i.My_GPIO_Init)
    NMI_Handler                              0x08000e91   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08000e95   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08000f05   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    OLED_Clear                               0x08000f19   Thumb Code    36  oled.o(i.OLED_Clear)
    OLED_ClearArea                           0x08000f41   Thumb Code   140  oled.o(i.OLED_ClearArea)
    OLED_GPIO_Init                           0x08000fd1   Thumb Code    92  oled.o(i.OLED_GPIO_Init)
    OLED_I2C_SendByte                        0x08001031   Thumb Code    62  oled.o(i.OLED_I2C_SendByte)
    OLED_I2C_Start                           0x0800106f   Thumb Code    28  oled.o(i.OLED_I2C_Start)
    OLED_I2C_Stop                            0x0800108b   Thumb Code    22  oled.o(i.OLED_I2C_Stop)
    OLED_Init                                0x080010a1   Thumb Code   154  oled.o(i.OLED_Init)
    OLED_SetCursor                           0x0800113b   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x0800115d   Thumb Code    74  oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x080011b1   Thumb Code   248  oled.o(i.OLED_ShowImage)
    OLED_ShowString                          0x080012ad   Thumb Code   442  oled.o(i.OLED_ShowString)
    OLED_Update                              0x08001471   Thumb Code    36  oled.o(i.OLED_Update)
    OLED_W_SCL                               0x08001499   Thumb Code    18  oled.o(i.OLED_W_SCL)
    OLED_W_SDA                               0x080014b1   Thumb Code    18  oled.o(i.OLED_W_SDA)
    OLED_WriteCommand                        0x080014c9   Thumb Code    32  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x080014e9   Thumb Code    46  oled.o(i.OLED_WriteData)
    PWM_Init                                 0x08001519   Thumb Code   156  pwm.o(i.PWM_Init)
    PWM_SetCompare3                          0x080015b9   Thumb Code    16  pwm.o(i.PWM_SetCompare3)
    PWM_SetCompare4                          0x080015c9   Thumb Code    16  pwm.o(i.PWM_SetCompare4)
    PendSV_Handler                           0x080015d9   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x080015dd   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x080015fd   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_GetClocksFreq                        0x0800161d   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x080016f1   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    Self_Left                                0x080016f3   Thumb Code    20  car.o(i.Self_Left)
    Self_Right                               0x08001707   Thumb Code    16  car.o(i.Self_Right)
    Serial_Init                              0x08001719   Thumb Code   174  serial.o(i.Serial_Init)
    SysTick_CLKSourceConfig                  0x080018b9   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x080018e1   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x080018e5   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM4_IRQHandler                          0x08001945   Thumb Code    24  main.o(i.TIM4_IRQHandler)
    TIM_ClearFlag                            0x08001961   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearFlag)
    TIM_ClearITPendingBit                    0x08001967   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x0800196d   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_GetITStatus                          0x08001985   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x080019a7   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_InternalClockConfig                  0x080019b9   Thumb Code    12  stm32f10x_tim.o(i.TIM_InternalClockConfig)
    TIM_OC3Init                              0x080019c5   Thumb Code   150  stm32f10x_tim.o(i.TIM_OC3Init)
    TIM_OC4Init                              0x08001a65   Thumb Code   114  stm32f10x_tim.o(i.TIM_OC4Init)
    TIM_OCStructInit                         0x08001ae1   Thumb Code    20  stm32f10x_tim.o(i.TIM_OCStructInit)
    TIM_SetCompare3                          0x08001af5   Thumb Code     4  stm32f10x_tim.o(i.TIM_SetCompare3)
    TIM_SetCompare4                          0x08001af9   Thumb Code     6  stm32f10x_tim.o(i.TIM_SetCompare4)
    TIM_TimeBaseInit                         0x08001b01   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    Timer1_Init                              0x08001ba5   Thumb Code   122  timer.o(i.Timer1_Init)
    Turn_Left                                0x08001c25   Thumb Code    18  car.o(i.Turn_Left)
    Turn_Right                               0x08001c37   Thumb Code    16  car.o(i.Turn_Right)
    USART1_IRQHandler                        0x08001c49   Thumb Code   122  main.o(i.USART1_IRQHandler)
    USART_ClearITPendingBit                  0x08001ccd   Thumb Code    30  stm32f10x_usart.o(i.USART_ClearITPendingBit)
    USART_Cmd                                0x08001ceb   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08001d03   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08001d57   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08001da1   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08001e79   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x08001e83   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08001e87   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __kernel_poly                            0x08001eaf   Thumb Code   170  poly.o(i.__kernel_poly)
    __mathlib_dbl_infnan                     0x08001f59   Thumb Code     6  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x08001f5f   Thumb Code     4  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x08001f63   Thumb Code    12  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x08001f71   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    asin                                     0x08002029   Thumb Code   572  asin.o(i.asin)
    atan                                     0x08002299   Thumb Code   474  atan.o(i.atan)
    atan2                                    0x080024b9   Thumb Code   346  atan2.o(i.atan2)
    delay_init                               0x0800269d   Thumb Code    50  delay.o(i.delay_init)
    delay_ms                                 0x080026e1   Thumb Code    72  delay.o(i.delay_ms)
    delay_us                                 0x0800272d   Thumb Code    72  delay.o(i.delay_us)
    dmp_enable_6x_lp_quat                    0x08002779   Thumb Code    60  inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat)
    dmp_enable_feature                       0x080027b5   Thumb Code   530  inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature)
    dmp_enable_gyro_cal                      0x080029d1   Thumb Code    62  inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal)
    dmp_enable_lp_quat                       0x08002a29   Thumb Code    60  inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat)
    dmp_load_motion_driver_firmware          0x08002a65   Thumb Code    20  inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware)
    dmp_read_fifo                            0x08002a7d   Thumb Code   450  inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo)
    dmp_set_accel_bias                       0x08002c45   Thumb Code   300  inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias)
    dmp_set_fifo_rate                        0x08002d75   Thumb Code    96  inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate)
    dmp_set_gyro_bias                        0x08002de5   Thumb Code   294  inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias)
    dmp_set_orientation                      0x08002f15   Thumb Code   290  inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation)
    dmp_set_shake_reject_thresh              0x0800304d   Thumb Code    56  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh)
    dmp_set_shake_reject_time                0x08003085   Thumb Code    38  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time)
    dmp_set_shake_reject_timeout             0x080030ab   Thumb Code    38  inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout)
    dmp_set_tap_axes                         0x080030d1   Thumb Code    70  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes)
    dmp_set_tap_count                        0x08003117   Thumb Code    38  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count)
    dmp_set_tap_thresh                       0x0800313d   Thumb Code   396  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh)
    dmp_set_tap_time                         0x080032dd   Thumb Code    38  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time)
    dmp_set_tap_time_multi                   0x08003303   Thumb Code    38  inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi)
    inv_orientation_matrix_to_scalar         0x0800395d   Thumb Code    40  inv_mpu.o(i.inv_orientation_matrix_to_scalar)
    inv_row_2_scale                          0x08003985   Thumb Code    78  inv_mpu.o(i.inv_row_2_scale)
    main                                     0x080039d5   Thumb Code    52  main.o(i.main)
    mpu6050_read                             0x08003a0d   Thumb Code    92  mpu6050.o(i.mpu6050_read)
    mpu6050_write                            0x08003a69   Thumb Code    68  mpu6050.o(i.mpu6050_write)
    mpu_configure_fifo                       0x08003aad   Thumb Code   106  inv_mpu.o(i.mpu_configure_fifo)
    mpu_get_accel_fsr                        0x08003b1d   Thumb Code    72  inv_mpu.o(i.mpu_get_accel_fsr)
    mpu_get_accel_sens                       0x08003b69   Thumb Code    78  inv_mpu.o(i.mpu_get_accel_sens)
    mpu_get_fifo_config                      0x08003bbd   Thumb Code    12  inv_mpu.o(i.mpu_get_fifo_config)
    mpu_get_gyro_fsr                         0x08003bcd   Thumb Code    64  inv_mpu.o(i.mpu_get_gyro_fsr)
    mpu_get_gyro_sens                        0x08003c11   Thumb Code    58  inv_mpu.o(i.mpu_get_gyro_sens)
    mpu_get_lpf                              0x08003c61   Thumb Code    74  inv_mpu.o(i.mpu_get_lpf)
    mpu_get_sample_rate                      0x08003cb1   Thumb Code    26  inv_mpu.o(i.mpu_get_sample_rate)
    mpu_init                                 0x08003cd1   Thumb Code   396  inv_mpu.o(i.mpu_init)
    mpu_load_firmware                        0x08003e61   Thumb Code   180  inv_mpu.o(i.mpu_load_firmware)
    mpu_lp_accel_mode                        0x08003f19   Thumb Code   218  inv_mpu.o(i.mpu_lp_accel_mode)
    mpu_read_fifo_stream                     0x08003ff9   Thumb Code   186  inv_mpu.o(i.mpu_read_fifo_stream)
    mpu_read_mem                             0x080040b9   Thumb Code   122  inv_mpu.o(i.mpu_read_mem)
    mpu_reset_fifo                           0x08004139   Thumb Code   450  inv_mpu.o(i.mpu_reset_fifo)
    mpu_run_self_test                        0x08004301   Thumb Code   278  inv_mpu.o(i.mpu_run_self_test)
    mpu_set_accel_fsr                        0x0800441d   Thumb Code   126  inv_mpu.o(i.mpu_set_accel_fsr)
    mpu_set_bypass                           0x080044a1   Thumb Code   328  inv_mpu.o(i.mpu_set_bypass)
    mpu_set_dmp_state                        0x080045ed   Thumb Code   138  inv_mpu.o(i.mpu_set_dmp_state)
    mpu_set_gyro_fsr                         0x0800467d   Thumb Code   132  inv_mpu.o(i.mpu_set_gyro_fsr)
    mpu_set_int_latched                      0x08004705   Thumb Code   102  inv_mpu.o(i.mpu_set_int_latched)
    mpu_set_lpf                              0x08004771   Thumb Code   126  inv_mpu.o(i.mpu_set_lpf)
    mpu_set_sample_rate                      0x080047f5   Thumb Code   152  inv_mpu.o(i.mpu_set_sample_rate)
    mpu_set_sensors                          0x08004891   Thumb Code   202  inv_mpu.o(i.mpu_set_sensors)
    mpu_write_mem                            0x08004961   Thumb Code   122  inv_mpu.o(i.mpu_write_mem)
    power                                    0x080049e1   Thumb Code    20  sys.o(i.power)
    run_self_test                            0x080049f5   Thumb Code   148  inv_mpu.o(i.run_self_test)
    sqrt                                     0x08004b19   Thumb Code    76  sqrt.o(i.sqrt)
    __aeabi_d2f                              0x08004b65   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08004b65   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08004bc9   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08004bc9   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08004d19   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __aeabi_ddiv                             0x08004d29   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08004d29   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_dmul                             0x08004fd9   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08004fd9   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800512d   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x080051c9   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x080051d5   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x080051d5   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x080051ed   Thumb Code   456  dsqrt_noumaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x080053b9   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x080053b9   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x0800558d   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x0800558d   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x080055e5   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x080055e5   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcmp_Inf                           0x080056a9   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x080056c1   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080056c1   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_cfcmpeq                          0x08005845   Thumb Code     0  feqf.o(x$fpl$feqf)
    _fcmpeq                                  0x08005845   Thumb Code   104  feqf.o(x$fpl$feqf)
    __aeabi_f2iz                             0x080058ad   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x080058ad   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_f2uiz                            0x080058e5   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x080058e5   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x08005925   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08005925   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x08005955   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08005955   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    __aeabi_cfcmple                          0x0800597d   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x0800597d   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x080059cf   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x080059e5   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x080059e5   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08005ae7   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08005b73   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x08005b7d   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x08005b7d   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_fsub                             0x08005be1   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08005be1   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __fpl_return_NaN                         0x08005ccb   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x08005d2f   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x08005d8b   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x08005dba   Number         0  usenofp.o(x$fpl$usenofp)
    hw                                       0x08005dbc   Data          12  inv_mpu.o(.constdata)
    reg                                      0x08005dc8   Data          27  inv_mpu.o(.constdata)
    test                                     0x08005de4   Data          40  inv_mpu.o(.constdata)
    OLED_F8x16                               0x08006a02   Data        1520  oled_data.o(.constdata)
    OLED_F6x8                                0x08006ff2   Data         570  oled_data.o(.constdata)
    OLED_CF16x16                             0x0800722c   Data         259  oled_data.o(.constdata)
    Diode                                    0x0800732f   Data          32  oled_data.o(.constdata)
    __mathlib_zero                           0x08007438   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x08007440   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08007460   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f10x.o(.data)
    Pitch                                    0x20000060   Data           4  mpu_exti.o(.data)
    Roll                                     0x20000064   Data           4  mpu_exti.o(.data)
    Yaw                                      0x20000068   Data           4  mpu_exti.o(.data)
    Data1                                    0x20000070   Data           2  main.o(.data)
    a                                        0x20000072   Data           2  main.o(.data)
    Num                                      0x20000074   Data           2  main.o(.data)
    OLED_DisplayBuf                          0x20000088   Data        1024  oled.o(.bss)
    __libspace_start                         0x20000488   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200004e8   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x000074d8, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Base: 0x08000000, Size: 0x00007460, Max: 0x00010000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f10x_md.o
    0x080000ec   0x00000008   Code   RO         4593  * !!!main             c_w.l(__main.o)
    0x080000f4   0x00000034   Code   RO         5079    !!!scatter          c_w.l(__scatter.o)
    0x08000128   0x0000001a   Code   RO         5081    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000142   0x00000002   PAD
    0x08000144   0x0000001c   Code   RO         5083    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000160   0x00000002   Code   RO         4940    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000162   0x00000000   Code   RO         4955    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4957    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4960    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4962    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4964    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4967    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4969    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4971    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4973    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4975    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4977    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4979    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4981    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4983    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4985    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4987    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4991    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4993    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4995    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000162   0x00000000   Code   RO         4997    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000162   0x00000002   Code   RO         4998    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000164   0x00000002   Code   RO         5030    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000166   0x00000000   Code   RO         5048    .ARM.Collect$$libshutdown$$00000003  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         5051    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         5054    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         5056    .ARM.Collect$$libshutdown$$0000000B  c_w.l(libshutdown2.o)
    0x08000166   0x00000000   Code   RO         5059    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000166   0x00000002   Code   RO         5060    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000168   0x00000000   Code   RO         4681    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000168   0x00000000   Code   RO         4842    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000168   0x00000006   Code   RO         4854    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800016e   0x00000000   Code   RO         4844    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800016e   0x00000004   Code   RO         4845    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000172   0x00000000   Code   RO         4847    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000172   0x00000008   Code   RO         4848    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800017a   0x00000002   Code   RO         4945    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800017c   0x00000000   Code   RO         5002    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800017c   0x00000004   Code   RO         5003    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000180   0x00000006   Code   RO         5004    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000186   0x00000002   PAD
    0x08000188   0x00000040   Code   RO            4    .text               startup_stm32f10x_md.o
    0x080001c8   0x00000048   Code   RO         4581    .text               c_w.l(llsdiv.o)
    0x08000210   0x00000058   Code   RO         4585    .text               c_w.l(memcmp.o)
    0x08000268   0x00000010   Code   RO         4587    .text               c_w.l(aeabi_memset.o)
    0x08000278   0x00000080   Code   RO         4589    .text               c_w.l(strcmpv7m.o)
    0x080002f8   0x00000006   Code   RO         4591    .text               c_w.l(heapauxi.o)
    0x080002fe   0x000000ee   Code   RO         4682    .text               c_w.l(lludivv7m.o)
    0x080003ec   0x00000016   Code   RO         4684    .text               c_w.l(_rserrno.o)
    0x08000402   0x00000044   Code   RO         4777    .text               c_w.l(rt_memclr.o)
    0x08000446   0x00000002   PAD
    0x08000448   0x00000008   Code   RO         4863    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000450   0x0000004e   Code   RO         4883    .text               c_w.l(rt_memclr_w.o)
    0x0800049e   0x00000002   PAD
    0x080004a0   0x00000008   Code   RO         4889    .text               c_w.l(libspace.o)
    0x080004a8   0x0000004a   Code   RO         4892    .text               c_w.l(sys_stackheap_outer.o)
    0x080004f2   0x0000000c   Code   RO         4933    .text               c_w.l(exit.o)
    0x080004fe   0x00000002   PAD
    0x08000500   0x0000000c   Code   RO         5018    .text               c_w.l(sys_exit.o)
    0x0800050c   0x00000002   Code   RO         5035    .text               c_w.l(use_no_semi.o)
    0x0800050e   0x00000000   Code   RO         5037    .text               c_w.l(indicate_semi.o)
    0x0800050e   0x00000004   Code   RO         4516    i.BusFault_Handler  stm32f10x_it.o
    0x08000512   0x00000008   Code   RO         3276    i.Car_Init          car.o
    0x0800051a   0x00000010   Code   RO         3277    i.Car_Stop          car.o
    0x0800052a   0x00000002   Code   RO         4517    i.DebugMon_Handler  stm32f10x_it.o
    0x0800052c   0x00000030   Code   RO         3994    i.EXTI15_10_IRQHandler  mpu_exti.o
    0x0800055c   0x0000000c   Code   RO          985    i.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x08000568   0x00000028   Code   RO          989    i.EXTI_GetITStatus  stm32f10x_exti.o
    0x08000590   0x00000094   Code   RO          990    i.EXTI_Init         stm32f10x_exti.o
    0x08000624   0x00000040   Code   RO         1344    i.GPIO_EXTILineConfig  stm32f10x_gpio.o
    0x08000664   0x00000116   Code   RO         1347    i.GPIO_Init         stm32f10x_gpio.o
    0x0800077a   0x00000004   Code   RO         1354    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x0800077e   0x00000004   Code   RO         1355    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08000782   0x0000000a   Code   RO         1358    i.GPIO_WriteBit     stm32f10x_gpio.o
    0x0800078c   0x00000012   Code   RO         3278    i.Go_Ahead          car.o
    0x0800079e   0x00000012   Code   RO         3279    i.Go_Back           car.o
    0x080007b0   0x00000004   Code   RO         4518    i.HardFault_Handler  stm32f10x_it.o
    0x080007b4   0x0000002c   Code   RO         3429    i.Infrared_Init     track.o
    0x080007e0   0x00000224   Code   RO         3475    i.MPU6050_DMP_Get_Data  inv_mpu.o
    0x08000a04   0x00000050   Code   RO         3476    i.MPU6050_DMP_Init  inv_mpu.o
    0x08000a54   0x00000034   Code   RO         4051    i.MPU6050_IIC_IO_Init  mpu6050_i2c.o
    0x08000a88   0x00000050   Code   RO         4052    i.MPU6050_IIC_Read_Ack  mpu6050_i2c.o
    0x08000ad8   0x00000060   Code   RO         4053    i.MPU6050_IIC_Read_Byte  mpu6050_i2c.o
    0x08000b38   0x0000001c   Code   RO         4054    i.MPU6050_IIC_SDA_IO_IN  mpu6050_i2c.o
    0x08000b54   0x00000018   Code   RO         4055    i.MPU6050_IIC_SDA_IO_OUT  mpu6050_i2c.o
    0x08000b6c   0x00000038   Code   RO         4056    i.MPU6050_IIC_Send_Ack  mpu6050_i2c.o
    0x08000ba4   0x00000064   Code   RO         4057    i.MPU6050_IIC_Send_Byte  mpu6050_i2c.o
    0x08000c08   0x00000038   Code   RO         4058    i.MPU6050_IIC_Start  mpu6050_i2c.o
    0x08000c40   0x0000003c   Code   RO         4059    i.MPU6050_IIC_Stop  mpu6050_i2c.o
    0x08000c7c   0x00000080   Code   RO         3995    i.MPU_EXTI_Init     mpu_exti.o
    0x08000cfc   0x00000004   Code   RO         4519    i.MemManage_Handler  stm32f10x_it.o
    0x08000d00   0x00000030   Code   RO         3249    i.Motor_Init        motor.o
    0x08000d30   0x00000058   Code   RO         3250    i.Motor_SetLeftSpeed  motor.o
    0x08000d88   0x00000058   Code   RO         3251    i.Motor_SetRightSpeed  motor.o
    0x08000de0   0x000000b0   Code   RO         4402    i.My_GPIO_Init      sys.o
    0x08000e90   0x00000002   Code   RO         4520    i.NMI_Handler       stm32f10x_it.o
    0x08000e92   0x00000002   PAD
    0x08000e94   0x00000070   Code   RO          137    i.NVIC_Init         misc.o
    0x08000f04   0x00000014   Code   RO          138    i.NVIC_PriorityGroupConfig  misc.o
    0x08000f18   0x00000028   Code   RO         4111    i.OLED_Clear        oled.o
    0x08000f40   0x00000090   Code   RO         4112    i.OLED_ClearArea    oled.o
    0x08000fd0   0x00000060   Code   RO         4120    i.OLED_GPIO_Init    oled.o
    0x08001030   0x0000003e   Code   RO         4122    i.OLED_I2C_SendByte  oled.o
    0x0800106e   0x0000001c   Code   RO         4123    i.OLED_I2C_Start    oled.o
    0x0800108a   0x00000016   Code   RO         4124    i.OLED_I2C_Stop     oled.o
    0x080010a0   0x0000009a   Code   RO         4125    i.OLED_Init         oled.o
    0x0800113a   0x00000022   Code   RO         4131    i.OLED_SetCursor    oled.o
    0x0800115c   0x00000054   Code   RO         4133    i.OLED_ShowChar     oled.o
    0x080011b0   0x000000fc   Code   RO         4136    i.OLED_ShowImage    oled.o
    0x080012ac   0x000001c4   Code   RO         4139    i.OLED_ShowString   oled.o
    0x08001470   0x00000028   Code   RO         4140    i.OLED_Update       oled.o
    0x08001498   0x00000018   Code   RO         4142    i.OLED_W_SCL        oled.o
    0x080014b0   0x00000018   Code   RO         4143    i.OLED_W_SDA        oled.o
    0x080014c8   0x00000020   Code   RO         4144    i.OLED_WriteCommand  oled.o
    0x080014e8   0x0000002e   Code   RO         4145    i.OLED_WriteData    oled.o
    0x08001516   0x00000002   PAD
    0x08001518   0x000000a0   Code   RO         3225    i.PWM_Init          pwm.o
    0x080015b8   0x00000010   Code   RO         3226    i.PWM_SetCompare3   pwm.o
    0x080015c8   0x00000010   Code   RO         3227    i.PWM_SetCompare4   pwm.o
    0x080015d8   0x00000002   Code   RO         4521    i.PendSV_Handler    stm32f10x_it.o
    0x080015da   0x00000002   PAD
    0x080015dc   0x00000020   Code   RO         1775    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x080015fc   0x00000020   Code   RO         1777    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x0800161c   0x000000d4   Code   RO         1785    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x080016f0   0x00000002   Code   RO         4522    i.SVC_Handler       stm32f10x_it.o
    0x080016f2   0x00000014   Code   RO         3280    i.Self_Left         car.o
    0x08001706   0x00000010   Code   RO         3281    i.Self_Right        car.o
    0x08001716   0x00000002   PAD
    0x08001718   0x000000b8   Code   RO         3368    i.Serial_Init       serial.o
    0x080017d0   0x00000008   Code   RO           24    i.SetSysClock       system_stm32f10x.o
    0x080017d8   0x000000e0   Code   RO           25    i.SetSysClockTo72   system_stm32f10x.o
    0x080018b8   0x00000028   Code   RO          141    i.SysTick_CLKSourceConfig  misc.o
    0x080018e0   0x00000002   Code   RO         4523    i.SysTick_Handler   stm32f10x_it.o
    0x080018e2   0x00000002   PAD
    0x080018e4   0x00000060   Code   RO           27    i.SystemInit        system_stm32f10x.o
    0x08001944   0x0000001c   Code   RO         4445    i.TIM4_IRQHandler   main.o
    0x08001960   0x00000006   Code   RO         2415    i.TIM_ClearFlag     stm32f10x_tim.o
    0x08001966   0x00000006   Code   RO         2416    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x0800196c   0x00000018   Code   RO         2421    i.TIM_Cmd           stm32f10x_tim.o
    0x08001984   0x00000022   Code   RO         2442    i.TIM_GetITStatus   stm32f10x_tim.o
    0x080019a6   0x00000012   Code   RO         2446    i.TIM_ITConfig      stm32f10x_tim.o
    0x080019b8   0x0000000c   Code   RO         2448    i.TIM_InternalClockConfig  stm32f10x_tim.o
    0x080019c4   0x000000a0   Code   RO         2460    i.TIM_OC3Init       stm32f10x_tim.o
    0x08001a64   0x0000007c   Code   RO         2465    i.TIM_OC4Init       stm32f10x_tim.o
    0x08001ae0   0x00000014   Code   RO         2468    i.TIM_OCStructInit  stm32f10x_tim.o
    0x08001af4   0x00000004   Code   RO         2484    i.TIM_SetCompare3   stm32f10x_tim.o
    0x08001af8   0x00000006   Code   RO         2485    i.TIM_SetCompare4   stm32f10x_tim.o
    0x08001afe   0x00000002   PAD
    0x08001b00   0x000000a4   Code   RO         2492    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08001ba4   0x00000080   Code   RO         3463    i.Timer1_Init       timer.o
    0x08001c24   0x00000012   Code   RO         3282    i.Turn_Left         car.o
    0x08001c36   0x00000010   Code   RO         3283    i.Turn_Right        car.o
    0x08001c46   0x00000002   PAD
    0x08001c48   0x00000084   Code   RO         4446    i.USART1_IRQHandler  main.o
    0x08001ccc   0x0000001e   Code   RO         2957    i.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08001cea   0x00000018   Code   RO         2960    i.USART_Cmd         stm32f10x_usart.o
    0x08001d02   0x00000054   Code   RO         2964    i.USART_GetITStatus  stm32f10x_usart.o
    0x08001d56   0x0000004a   Code   RO         2966    i.USART_ITConfig    stm32f10x_usart.o
    0x08001da0   0x000000d8   Code   RO         2967    i.USART_Init        stm32f10x_usart.o
    0x08001e78   0x0000000a   Code   RO         2974    i.USART_ReceiveData  stm32f10x_usart.o
    0x08001e82   0x00000004   Code   RO         4524    i.UsageFault_Handler  stm32f10x_it.o
    0x08001e86   0x00000028   Code   RO         4828    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08001eae   0x000000aa   Code   RO         4830    i.__kernel_poly     m_ws.l(poly.o)
    0x08001f58   0x00000006   Code   RO         4815    i.__mathlib_dbl_infnan  m_ws.l(dunder.o)
    0x08001f5e   0x00000004   Code   RO         4816    i.__mathlib_dbl_infnan2  m_ws.l(dunder.o)
    0x08001f62   0x0000000c   Code   RO         4817    i.__mathlib_dbl_invalid  m_ws.l(dunder.o)
    0x08001f6e   0x00000002   PAD
    0x08001f70   0x00000010   Code   RO         4820    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x08001f80   0x000000a8   Code   RO         3477    i.accel_self_test   inv_mpu.o
    0x08002028   0x00000270   Code   RO         4662    i.asin              m_ws.l(asin.o)
    0x08002298   0x00000220   Code   RO         4805    i.atan              m_ws.l(atan.o)
    0x080024b8   0x00000180   Code   RO         4672    i.atan2             m_ws.l(atan2.o)
    0x08002638   0x00000064   Code   RO         3814    i.decode_gesture    inv_mpu_dmp_motion_driver.o
    0x0800269c   0x00000044   Code   RO         4367    i.delay_init        delay.o
    0x080026e0   0x0000004c   Code   RO         4368    i.delay_ms          delay.o
    0x0800272c   0x0000004c   Code   RO         4369    i.delay_us          delay.o
    0x08002778   0x0000003c   Code   RO         3815    i.dmp_enable_6x_lp_quat  inv_mpu_dmp_motion_driver.o
    0x080027b4   0x0000021c   Code   RO         3816    i.dmp_enable_feature  inv_mpu_dmp_motion_driver.o
    0x080029d0   0x00000058   Code   RO         3817    i.dmp_enable_gyro_cal  inv_mpu_dmp_motion_driver.o
    0x08002a28   0x0000003c   Code   RO         3818    i.dmp_enable_lp_quat  inv_mpu_dmp_motion_driver.o
    0x08002a64   0x00000018   Code   RO         3823    i.dmp_load_motion_driver_firmware  inv_mpu_dmp_motion_driver.o
    0x08002a7c   0x000001c8   Code   RO         3824    i.dmp_read_fifo     inv_mpu_dmp_motion_driver.o
    0x08002c44   0x00000130   Code   RO         3827    i.dmp_set_accel_bias  inv_mpu_dmp_motion_driver.o
    0x08002d74   0x00000070   Code   RO         3828    i.dmp_set_fifo_rate  inv_mpu_dmp_motion_driver.o
    0x08002de4   0x00000130   Code   RO         3829    i.dmp_set_gyro_bias  inv_mpu_dmp_motion_driver.o
    0x08002f14   0x00000138   Code   RO         3831    i.dmp_set_orientation  inv_mpu_dmp_motion_driver.o
    0x0800304c   0x00000038   Code   RO         3834    i.dmp_set_shake_reject_thresh  inv_mpu_dmp_motion_driver.o
    0x08003084   0x00000026   Code   RO         3835    i.dmp_set_shake_reject_time  inv_mpu_dmp_motion_driver.o
    0x080030aa   0x00000026   Code   RO         3836    i.dmp_set_shake_reject_timeout  inv_mpu_dmp_motion_driver.o
    0x080030d0   0x00000046   Code   RO         3837    i.dmp_set_tap_axes  inv_mpu_dmp_motion_driver.o
    0x08003116   0x00000026   Code   RO         3838    i.dmp_set_tap_count  inv_mpu_dmp_motion_driver.o
    0x0800313c   0x000001a0   Code   RO         3839    i.dmp_set_tap_thresh  inv_mpu_dmp_motion_driver.o
    0x080032dc   0x00000026   Code   RO         3840    i.dmp_set_tap_time  inv_mpu_dmp_motion_driver.o
    0x08003302   0x00000026   Code   RO         3841    i.dmp_set_tap_time_multi  inv_mpu_dmp_motion_driver.o
    0x08003328   0x000000b4   Code   RO         3478    i.get_accel_prod_shift  inv_mpu.o
    0x080033dc   0x00000470   Code   RO         3480    i.get_st_biases     inv_mpu.o
    0x0800384c   0x00000110   Code   RO         3481    i.gyro_self_test    inv_mpu.o
    0x0800395c   0x00000028   Code   RO         3482    i.inv_orientation_matrix_to_scalar  inv_mpu.o
    0x08003984   0x0000004e   Code   RO         3483    i.inv_row_2_scale   inv_mpu.o
    0x080039d2   0x00000002   PAD
    0x080039d4   0x00000038   Code   RO         4447    i.main              main.o
    0x08003a0c   0x0000005c   Code   RO         4016    i.mpu6050_read      mpu6050.o
    0x08003a68   0x00000044   Code   RO         4018    i.mpu6050_write     mpu6050.o
    0x08003aac   0x00000070   Code   RO         3484    i.mpu_configure_fifo  inv_mpu.o
    0x08003b1c   0x0000004c   Code   RO         3485    i.mpu_get_accel_fsr  inv_mpu.o
    0x08003b68   0x00000054   Code   RO         3487    i.mpu_get_accel_sens  inv_mpu.o
    0x08003bbc   0x00000010   Code   RO         3492    i.mpu_get_fifo_config  inv_mpu.o
    0x08003bcc   0x00000044   Code   RO         3493    i.mpu_get_gyro_fsr  inv_mpu.o
    0x08003c10   0x00000050   Code   RO         3495    i.mpu_get_gyro_sens  inv_mpu.o
    0x08003c60   0x00000050   Code   RO         3497    i.mpu_get_lpf       inv_mpu.o
    0x08003cb0   0x00000020   Code   RO         3499    i.mpu_get_sample_rate  inv_mpu.o
    0x08003cd0   0x00000190   Code   RO         3501    i.mpu_init          inv_mpu.o
    0x08003e60   0x000000b8   Code   RO         3502    i.mpu_load_firmware  inv_mpu.o
    0x08003f18   0x000000e0   Code   RO         3503    i.mpu_lp_accel_mode  inv_mpu.o
    0x08003ff8   0x000000c0   Code   RO         3506    i.mpu_read_fifo_stream  inv_mpu.o
    0x080040b8   0x00000080   Code   RO         3507    i.mpu_read_mem      inv_mpu.o
    0x08004138   0x000001c8   Code   RO         3510    i.mpu_reset_fifo    inv_mpu.o
    0x08004300   0x0000011c   Code   RO         3511    i.mpu_run_self_test  inv_mpu.o
    0x0800441c   0x00000084   Code   RO         3513    i.mpu_set_accel_fsr  inv_mpu.o
    0x080044a0   0x0000014c   Code   RO         3514    i.mpu_set_bypass    inv_mpu.o
    0x080045ec   0x00000090   Code   RO         3516    i.mpu_set_dmp_state  inv_mpu.o
    0x0800467c   0x00000088   Code   RO         3517    i.mpu_set_gyro_fsr  inv_mpu.o
    0x08004704   0x0000006c   Code   RO         3518    i.mpu_set_int_latched  inv_mpu.o
    0x08004770   0x00000084   Code   RO         3520    i.mpu_set_lpf       inv_mpu.o
    0x080047f4   0x0000009c   Code   RO         3521    i.mpu_set_sample_rate  inv_mpu.o
    0x08004890   0x000000d0   Code   RO         3522    i.mpu_set_sensors   inv_mpu.o
    0x08004960   0x00000080   Code   RO         3523    i.mpu_write_mem     inv_mpu.o
    0x080049e0   0x00000014   Code   RO         4405    i.power             sys.o
    0x080049f4   0x00000094   Code   RO         3524    i.run_self_test     inv_mpu.o
    0x08004a88   0x00000090   Code   RO         3525    i.set_int_enable    inv_mpu.o
    0x08004b18   0x0000004c   Code   RO         4834    i.sqrt              m_ws.l(sqrt.o)
    0x08004b64   0x00000062   Code   RO         4595    x$fpl$d2f           fz_ws.l(d2f.o)
    0x08004bc6   0x00000002   PAD
    0x08004bc8   0x00000150   Code   RO         4597    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x08004d18   0x00000010   Code   RO         4885    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x08004d28   0x000002b0   Code   RO         4604    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x08004fd8   0x00000154   Code   RO         4623    x$fpl$dmul          fz_ws.l(dmul.o)
    0x0800512c   0x0000009c   Code   RO         4781    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x080051c8   0x0000000c   Code   RO         4783    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x080051d4   0x00000016   Code   RO         4598    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x080051ea   0x00000002   PAD
    0x080051ec   0x000001cc   Code   RO         4887    x$fpl$dsqrt         fz_ws.l(dsqrt_noumaal.o)
    0x080053b8   0x000001d4   Code   RO         4599    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x0800558c   0x00000056   Code   RO         4627    x$fpl$f2d           fz_ws.l(f2d.o)
    0x080055e2   0x00000002   PAD
    0x080055e4   0x000000c4   Code   RO         4629    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x080056a8   0x00000018   Code   RO         4787    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x080056c0   0x00000184   Code   RO         4636    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08005844   0x00000068   Code   RO         4639    x$fpl$feqf          fz_ws.l(feqf.o)
    0x080058ac   0x00000036   Code   RO         4641    x$fpl$ffix          fz_ws.l(ffix.o)
    0x080058e2   0x00000002   PAD
    0x080058e4   0x0000003e   Code   RO         4645    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x08005922   0x00000002   PAD
    0x08005924   0x00000030   Code   RO         4650    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08005954   0x00000026   Code   RO         4649    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x0800597a   0x00000002   PAD
    0x0800597c   0x00000068   Code   RO         4655    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x080059e4   0x00000102   Code   RO         4657    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08005ae6   0x0000008c   Code   RO         4789    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08005b72   0x0000000a   Code   RO         4791    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08005b7c   0x00000062   Code   RO         4659    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x08005bde   0x00000002   PAD
    0x08005be0   0x000000ea   Code   RO         4631    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08005cca   0x00000064   Code   RO         4943    x$fpl$retnan        fz_ws.l(retnan.o)
    0x08005d2e   0x0000005c   Code   RO         4801    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x08005d8a   0x00000030   Code   RO         4999    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x08005dba   0x00000000   Code   RO         4803    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08005dba   0x00000002   PAD
    0x08005dbc   0x00000050   Data   RO         3526    .constdata          inv_mpu.o
    0x08005e0c   0x00000bf6   Data   RO         3843    .constdata          inv_mpu_dmp_motion_driver.o
    0x08006a02   0x0000094d   Data   RO         4343    .constdata          oled_data.o
    0x0800734f   0x00000001   PAD
    0x08007350   0x00000050   Data   RO         4663    .constdata          m_ws.l(asin.o)
    0x080073a0   0x00000098   Data   RO         4806    .constdata          m_ws.l(atan.o)
    0x08007438   0x00000008   Data   RO         4832    .constdata          m_ws.l(qnan.o)
    0x08007440   0x00000020   Data   RO         5077    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Base: 0x20000000, Size: 0x00000ae8, Max: 0x00005000, ABSOLUTE)

    Base Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00000014   Data   RW           28    .data               system_stm32f10x.o
    0x20000014   0x00000014   Data   RW         1805    .data               stm32f10x_rcc.o
    0x20000028   0x00000035   Data   RW         3527    .data               inv_mpu.o
    0x2000005d   0x00000003   PAD
    0x20000060   0x0000000c   Data   RW         3996    .data               mpu_exti.o
    0x2000006c   0x00000004   Data   RW         4370    .data               delay.o
    0x20000070   0x00000006   Data   RW         4448    .data               main.o
    0x20000076   0x00000002   PAD
    0x20000078   0x00000010   Zero   RW         3842    .bss                inv_mpu_dmp_motion_driver.o
    0x20000088   0x00000400   Zero   RW         4147    .bss                oled.o
    0x20000488   0x00000060   Zero   RW         4890    .bss                c_w.l(libspace.o)
    0x200004e8   0x00000200   Zero   RW            2    HEAP                startup_stm32f10x_md.o
    0x200006e8   0x00000400   Zero   RW            1    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       130          0          0          0          0       3317   car.o
         0          0          0          0          0       4496   core_cm3.o
       220         26          0          4          0       3475   delay.o
      6686        214         80         53          0      28362   inv_mpu.o
      3092        124       3062          0         16      19334   inv_mpu_dmp_motion_driver.o
       216         18          0          6          0       1627   main.o
       172         22          0          0          0     207317   misc.o
       224         14          0          0          0       1504   motor.o
       160          0          0          0          0       1631   mpu6050.o
       552         76          0          0          0       4644   mpu6050_i2c.o
       176         20          0         12          0       1296   mpu_exti.o
      1534         52          0          0       1024       9515   oled.o
         0          0       2381          0          0       1054   oled_data.o
       192          4          0          0          0       1531   pwm.o
       184         10          0          0          0        630   serial.o
        64         26        236          0       1536        804   startup_stm32f10x_md.o
         0          0          0          0          0       1648   stm32f10x_adc.o
       200         18          0          0          0       4036   stm32f10x_exti.o
       360          4          0          0          0      12254   stm32f10x_gpio.o
        26          0          0          0          0       3658   stm32f10x_it.o
       276         32          0         20          0      12922   stm32f10x_rcc.o
       578         62          0          0          0      27299   stm32f10x_tim.o
       438          6          0          0          0      11252   stm32f10x_usart.o
       196          4          0          0          0       1559   sys.o
       328         28          0         20          0      45417   system_stm32f10x.o
       128          6          0          0          0        538   timer.o
        44          4          0          0          0        468   track.o

    ----------------------------------------------------------------------
     16192        <USER>       <GROUP>        120       2576     411588   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        16          0          1          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        22          0          0          0          0        100   _rserrno.o
        16          0          0          0          0         68   aeabi_memset.o
        12          0          0          0          0         72   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
         2          0          0          0          0          0   libinit.o
         2          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        72          0          0          0          0         76   llsdiv.o
       238          0          0          0          0        100   lludivv7m.o
        88          0          0          0          0         76   memcmp.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        98          4          0          0          0         92   d2f.o
       826         16          0          0          0        348   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
       688        140          0          0          0        208   ddiv.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       460         56          0          0          0        120   dsqrt_noumaal.o
        86          4          0          0          0         84   f2d.o
       430          8          0          0          0        168   faddsub_clz.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
       104          4          0          0          0         84   feqf.o
        54          4          0          0          0         84   ffix.o
        62          4          0          0          0         84   ffixu.o
        86          0          0          0          0        136   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
       624         52         80          0          0        168   asin.o
       544         70        152          0          0        124   atan.o
       384         38          0          0          0        144   atan2.o
        38          6          0          0          0        272   dunder.o
        40          0          0          0          0         68   fpclassify.o
       170          0          0          0          0         96   poly.o
         0          0          8          0          0          0   qnan.o
        76          0          0          0          0         84   sqrt.o

    ----------------------------------------------------------------------
      7568        <USER>        <GROUP>          0         96       4732   Library Totals
        28          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       984         20          0          0         96       1280   c_w.l
      4680        348          0          0          0       2496   fz_ws.l
      1876        166        240          0          0        956   m_ws.l

    ----------------------------------------------------------------------
      7568        <USER>        <GROUP>          0         96       4732   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     23760       1304       6032        120       2672     406072   Grand Totals
     23760       1304       6032        120       2672     406072   ELF Image Totals
     23760       1304       6032        120          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                29792 (  29.09kB)
    Total RW  Size (RW Data + ZI Data)              2792 (   2.73kB)
    Total ROM Size (Code + RO Data + RW Data)      29912 (  29.21kB)

==============================================================================

