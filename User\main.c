#include "Device/Include/stm32f10x.h"   // Device header
#include "Delay.h"						
#include "PWM.h"
#include "Car.h"
#include "Ultrasound.h"
#include "Serial.h"
#include "Track.h"
#include "Motor.h"
#include "Encoder.h"
#include "OLED.h"
#include "Timer.h"
#include "MPU6050.h"
#include "MPU_EXTI.h"
#include "inv_mpu.h"

uint16_t Data1;
uint16_t a;
uint16_t Num;


int main(void)
{	
	
	//encoder_init();
	Car_Init();
	Serial_Init();
	//Ultrasound_Init();
	Infrared_Init();
	OLED_Init();
	Timer1_Init();
	delay_init();
	MPU_EXTI_Init();
	MPU6050_DMP_Init();
	
	while(1)
	{
//		OLED_ShowSignedNum(1,5,counterA,5);
//		OLED_ShowSignedNum(1,1,Pitch,5);
//		OLED_ShowSignedNum(2,1,Roll,5);
//		OLED_ShowSignedNum(3,1,Yaw,5);
//		OLED_Printf (0,16,OLED_8X16,"Pitch:%.2f        ",Pitch);
//		OLED_Printf (0,32,OLED_8X16,"Roll:%.2f         ",Roll);
//		OLED_Printf (0,48,OLED_8X16,"Yaw:%.2f          ",Yaw);
		OLED_ShowString(0,0,"...",OLED_8X16);
//		void track_start();
		OLED_Update ();
		
	}
}

void TIM4_IRQHandler(void)//??????
{
	if(TIM_GetITStatus(TIM4,TIM_IT_Update)==SET)
	{
		
		
		
		TIM_ClearITPendingBit(TIM4, TIM_IT_Update);
	}
}

void USART1_IRQHandler(void)
{
	if (USART_GetITStatus(USART1, USART_IT_RXNE) == SET)
	{
		Data1=USART_ReceiveData(USART1);
		if(Data1==0x30)Car_Stop();
		if(Data1==0x31)Go_Ahead();
		if(Data1==0x32)Go_Back();
		if(Data1==0x33)Turn_Left();
		if(Data1==0x34)Turn_Right();
		if(Data1==0x35)Self_Left();
		if(Data1==0x36)Self_Right();
		USART_ClearITPendingBit(USART1, USART_IT_RXNE);
	}
}



