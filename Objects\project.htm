<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\project.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\project.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5050106: Last Updated: Thu Jul 17 18:50:28 2025
<BR><P>
<H3>Maximum Stack Usage =        384 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
EXTI15_10_IRQHandler &rArr; MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[56]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[21]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[21]">ADC1_2_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[21]">ADC1_2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[9]">BusFault_Handler</a> from stm32f10x_it.o(i.BusFault_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[24]">CAN1_RX1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[25]">CAN1_SCE_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1c]">DMA1_Channel3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1d]">DMA1_Channel4_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1e]">DMA1_Channel5_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[1f]">DMA1_Channel6_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[20]">DMA1_Channel7_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[c]">DebugMon_Handler</a> from stm32f10x_it.o(i.DebugMon_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[15]">EXTI0_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[37]">EXTI15_10_IRQHandler</a> from mpu_exti.o(i.EXTI15_10_IRQHandler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[16]">EXTI1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[17]">EXTI2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[18]">EXTI3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[19]">EXTI4_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[26]">EXTI9_5_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[13]">FLASH_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[7]">HardFault_Handler</a> from stm32f10x_it.o(i.HardFault_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2f]">I2C1_ER_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2e]">I2C1_EV_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[31]">I2C2_ER_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[30]">I2C2_EV_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[8]">MemManage_Handler</a> from stm32f10x_it.o(i.MemManage_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[6]">NMI_Handler</a> from stm32f10x_it.o(i.NMI_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[10]">PVD_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[d]">PendSV_Handler</a> from stm32f10x_it.o(i.PendSV_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[14]">RCC_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[38]">RTCAlarm_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[12]">RTC_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[5]">Reset_Handler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[32]">SPI1_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[33]">SPI2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[b]">SVC_Handler</a> from stm32f10x_it.o(i.SVC_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[e]">SysTick_Handler</a> from stm32f10x_it.o(i.SysTick_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[3a]">SystemInit</a> from system_stm32f10x.o(i.SystemInit) referenced from startup_stm32f10x_md.o(.text)
 <LI><a href="#[11]">TAMPER_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[27]">TIM1_BRK_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2a]">TIM1_CC_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[29]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[28]">TIM1_UP_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2b]">TIM2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2c]">TIM3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[2d]">TIM4_IRQHandler</a> from main.o(i.TIM4_IRQHandler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[34]">USART1_IRQHandler</a> from main.o(i.USART1_IRQHandler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[35]">USART2_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[36]">USART3_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[39]">USBWakeUp_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[22]">USB_HP_CAN1_TX_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[23]">USB_LP_CAN1_RX0_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[a]">UsageFault_Handler</a> from stm32f10x_it.o(i.UsageFault_Handler) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[f]">WWDG_IRQHandler</a> from startup_stm32f10x_md.o(.text) referenced from startup_stm32f10x_md.o(RESET)
 <LI><a href="#[3c]">__main</a> from __main.o(!!!main) referenced from startup_stm32f10x_md.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[3c]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[3d]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[3f]"></a>__scatterload_rt2</STRONG> (Thumb, 44 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[11f]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[120]"></a>__scatterload_null</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[40]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[121]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[44]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[122]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[123]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002C))

<P><STRONG><a name="[124]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[125]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[126]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[127]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[128]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[129]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[12a]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[12b]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000A))

<P><STRONG><a name="[12c]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000011))

<P><STRONG><a name="[12d]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[12e]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[12f]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[130]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[131]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[132]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[133]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000033))

<P><STRONG><a name="[134]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[135]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[136]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[49]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[137]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000006))

<P><STRONG><a name="[138]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E))

<P><STRONG><a name="[139]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[13a]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000009))

<P><STRONG><a name="[13b]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000003))

<P><STRONG><a name="[13c]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000B))

<P><STRONG><a name="[3e]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[13d]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[41]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[43]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[13e]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[45]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 344 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; MPU6050_DMP_Init &rArr; run_self_test &rArr; mpu_run_self_test &rArr; mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[13f]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[57]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[48]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[140]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[4a]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[5]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USART2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_md.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f10x_md.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[ee]"></a>__aeabi_ldivmod</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, llsdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_ldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
</UL>

<P><STRONG><a name="[4c]"></a>_ll_sdiv</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, llsdiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>

<P><STRONG><a name="[f7]"></a>memcmp</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
</UL>

<P><STRONG><a name="[4e]"></a>__aeabi_memset</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, aeabi_memset.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
</UL>

<P><STRONG><a name="[a1]"></a>strcmp</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, strcmpv7m.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[141]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[142]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[143]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[4d]"></a>__aeabi_uldivmod</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ll_sdiv
</UL>

<P><STRONG><a name="[144]"></a>_ll_udiv</STRONG> (Thumb, 238 bytes, Stack size 48 bytes, lludivv7m.o(.text), UNUSED)

<P><STRONG><a name="[50]"></a>__read_errno</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, _rserrno.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>

<P><STRONG><a name="[52]"></a>__set_errno</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, _rserrno.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __set_errno
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_errno_addr
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[145]"></a>__aeabi_memclr</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)

<P><STRONG><a name="[53]"></a>__rt_memclr</STRONG> (Thumb, 68 bytes, Stack size 0 bytes, rt_memclr.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset_w
</UL>

<P><STRONG><a name="[4f]"></a>_memset</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[51]"></a>__aeabi_errno_addr</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__read_errno
</UL>

<P><STRONG><a name="[146]"></a>__errno$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[147]"></a>__rt_errno_addr$intlibspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_errno_addr_intlibspace.o(.text), UNUSED)

<P><STRONG><a name="[148]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[149]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[14a]"></a>__rt_memclr_w</STRONG> (Thumb, 78 bytes, Stack size 4 bytes, rt_memclr_w.o(.text), UNUSED)

<P><STRONG><a name="[54]"></a>_memset_w</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memclr_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memclr
</UL>

<P><STRONG><a name="[14b]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[55]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[14c]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[42]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[47]"></a>exit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, exit.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[4b]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[14d]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[14e]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[9]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.BusFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[14f]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[58]"></a>Car_Init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, car.o(i.Car_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Car_Init &rArr; Motor_Init &rArr; PWM_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5a]"></a>Car_Stop</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, car.o(i.Car_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Car_Stop &rArr; Motor_SetRightSpeed &rArr; PWM_SetCompare4
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetRightSpeed
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetLeftSpeed
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[c]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, mpu_exti.o(i.EXTI15_10_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 384<LI>Call Chain = EXTI15_10_IRQHandler &rArr; MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetITStatus
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_ClearITPendingBit
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>EXTI_ClearITPendingBit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_exti.o(i.EXTI_ClearITPendingBit))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[5d]"></a>EXTI_GetITStatus</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f10x_exti.o(i.EXTI_GetITStatus))
<BR><BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[89]"></a>EXTI_Init</STRONG> (Thumb, 142 bytes, Stack size 0 bytes, stm32f10x_exti.o(i.EXTI_Init))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_EXTI_Init
</UL>

<P><STRONG><a name="[88]"></a>GPIO_EXTILineConfig</STRONG> (Thumb, 60 bytes, Stack size 12 bytes, stm32f10x_gpio.o(i.GPIO_EXTILineConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = GPIO_EXTILineConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_EXTI_Init
</UL>

<P><STRONG><a name="[64]"></a>GPIO_Init</STRONG> (Thumb, 278 bytes, Stack size 24 bytes, stm32f10x_gpio.o(i.GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_GPIO_Init
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_EXTI_Init
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Infrared_Init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_Init
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
</UL>

<P><STRONG><a name="[8e]"></a>GPIO_ResetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_ResetBits))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetRightSpeed
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetLeftSpeed
</UL>

<P><STRONG><a name="[8d]"></a>GPIO_SetBits</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_SetBits))
<BR><BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetRightSpeed
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetLeftSpeed
</UL>

<P><STRONG><a name="[a3]"></a>GPIO_WriteBit</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_gpio.o(i.GPIO_WriteBit))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>

<P><STRONG><a name="[60]"></a>Go_Ahead</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, car.o(i.Go_Ahead))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Go_Ahead &rArr; Motor_SetRightSpeed &rArr; PWM_SetCompare4
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetRightSpeed
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetLeftSpeed
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[61]"></a>Go_Back</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, car.o(i.Go_Back))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Go_Back &rArr; Motor_SetRightSpeed &rArr; PWM_SetCompare4
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetRightSpeed
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetLeftSpeed
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[7]"></a>HardFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.HardFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>Infrared_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, track.o(i.Infrared_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Infrared_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5e]"></a>MPU6050_DMP_Get_Data</STRONG> (Thumb, 544 bytes, Stack size 136 bytes, inv_mpu.o(i.MPU6050_DMP_Get_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 376<LI>Call Chain = MPU6050_DMP_Get_Data &rArr; dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI15_10_IRQHandler
</UL>

<P><STRONG><a name="[70]"></a>MPU6050_DMP_Init</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, inv_mpu.o(i.MPU6050_DMP_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 344 + Unknown Stack Size
<LI>Call Chain = MPU6050_DMP_Init &rArr; run_self_test &rArr; mpu_run_self_test &rArr; mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_orientation
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_fifo_rate
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_load_motion_driver_firmware
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_IO_Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inv_orientation_matrix_to_scalar
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[71]"></a>MPU6050_IIC_IO_Init</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, mpu6050_i2c.o(i.MPU6050_IIC_IO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = MPU6050_IIC_IO_Init &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[7e]"></a>MPU6050_IIC_Read_Ack</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, mpu6050_i2c.o(i.MPU6050_IIC_Read_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_SDA_IO_IN
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Stop
</UL>
<BR>[Called By]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Send_Byte
</UL>

<P><STRONG><a name="[82]"></a>MPU6050_IIC_Read_Byte</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, mpu6050_i2c.o(i.MPU6050_IIC_Read_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = MPU6050_IIC_Read_Byte &rArr; MPU6050_IIC_Send_Ack &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Send_Ack
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_SDA_IO_IN
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
</UL>

<P><STRONG><a name="[7f]"></a>MPU6050_IIC_SDA_IO_IN</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_IN))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = MPU6050_IIC_SDA_IO_IN &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Read_Ack
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Read_Byte
</UL>

<P><STRONG><a name="[84]"></a>MPU6050_IIC_SDA_IO_OUT</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, mpu6050_i2c.o(i.MPU6050_IIC_SDA_IO_OUT))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Send_Ack
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Stop
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Start
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Send_Byte
</UL>

<P><STRONG><a name="[83]"></a>MPU6050_IIC_Send_Ack</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, mpu6050_i2c.o(i.MPU6050_IIC_Send_Ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = MPU6050_IIC_Send_Ack &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_SDA_IO_OUT
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Read_Byte
</UL>

<P><STRONG><a name="[85]"></a>MPU6050_IIC_Send_Byte</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, mpu6050_i2c.o(i.MPU6050_IIC_Send_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_SDA_IO_OUT
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Read_Ack
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
</UL>

<P><STRONG><a name="[86]"></a>MPU6050_IIC_Start</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, mpu6050_i2c.o(i.MPU6050_IIC_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = MPU6050_IIC_Start &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_SDA_IO_OUT
</UL>
<BR>[Called By]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
</UL>

<P><STRONG><a name="[81]"></a>MPU6050_IIC_Stop</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, mpu6050_i2c.o(i.MPU6050_IIC_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_SDA_IO_OUT
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Read_Ack
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
</UL>

<P><STRONG><a name="[87]"></a>MPU_EXTI_Init</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, mpu_exti.o(i.MPU_EXTI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = MPU_EXTI_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_EXTILineConfig
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.MemManage_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>Motor_Init</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, motor.o(i.Motor_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Motor_Init &rArr; PWM_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Init
</UL>

<P><STRONG><a name="[5b]"></a>Motor_SetLeftSpeed</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, motor.o(i.Motor_SetLeftSpeed))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Motor_SetLeftSpeed &rArr; PWM_SetCompare3
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_SetCompare3
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Turn_Right
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Turn_Left
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Self_Right
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Self_Left
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Go_Back
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Go_Ahead
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Stop
</UL>

<P><STRONG><a name="[5c]"></a>Motor_SetRightSpeed</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, motor.o(i.Motor_SetRightSpeed))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Motor_SetRightSpeed &rArr; PWM_SetCompare4
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_SetCompare4
</UL>
<BR>[Called By]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Turn_Right
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Turn_Left
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Self_Right
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Self_Left
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Go_Back
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Go_Ahead
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Stop
</UL>

<P><STRONG><a name="[7d]"></a>My_GPIO_Init</STRONG> (Thumb, 172 bytes, Stack size 32 bytes, sys.o(i.My_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;power
</UL>
<BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_SDA_IO_OUT
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_SDA_IO_IN
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_IO_Init
</UL>

<P><STRONG><a name="[6]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.NMI_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[8b]"></a>NVIC_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, misc.o(i.NVIC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_EXTI_Init
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_Init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_Init
</UL>

<P><STRONG><a name="[8a]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, misc.o(i.NVIC_PriorityGroupConfig))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_EXTI_Init
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_Init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_Init
</UL>

<P><STRONG><a name="[9a]"></a>OLED_Clear</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[9f]"></a>OLED_ClearArea</STRONG> (Thumb, 140 bytes, Stack size 28 bytes, oled.o(i.OLED_ClearArea))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = OLED_ClearArea
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowImage
</UL>

<P><STRONG><a name="[92]"></a>OLED_GPIO_Init</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, oled.o(i.OLED_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = OLED_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[95]"></a>OLED_I2C_SendByte</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, oled.o(i.OLED_I2C_SendByte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[96]"></a>OLED_I2C_Start</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, oled.o(i.OLED_I2C_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_I2C_Start &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[97]"></a>OLED_I2C_Stop</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, oled.o(i.OLED_I2C_Stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = OLED_I2C_Stop &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SDA
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_W_SCL
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>

<P><STRONG><a name="[98]"></a>OLED_Init</STRONG> (Thumb, 154 bytes, Stack size 8 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = OLED_Init &rArr; OLED_Update &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9c]"></a>OLED_SetCursor</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, oled.o(i.OLED_SetCursor))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
</UL>

<P><STRONG><a name="[9d]"></a>OLED_ShowChar</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = OLED_ShowChar &rArr; OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowImage
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[9e]"></a>OLED_ShowImage</STRONG> (Thumb, 248 bytes, Stack size 52 bytes, oled.o(i.OLED_ShowImage))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ClearArea
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[a0]"></a>OLED_ShowString</STRONG> (Thumb, 442 bytes, Stack size 48 bytes, oled.o(i.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_ShowImage &rArr; OLED_ClearArea
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcmp
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowImage
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9b]"></a>OLED_Update</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, oled.o(i.OLED_Update))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = OLED_Update &rArr; OLED_SetCursor &rArr; OLED_WriteCommand &rArr; OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WriteData
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[93]"></a>OLED_W_SCL</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, oled.o(i.OLED_W_SCL))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OLED_W_SCL
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
</UL>

<P><STRONG><a name="[94]"></a>OLED_W_SDA</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, oled.o(i.OLED_W_SDA))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
</UL>

<P><STRONG><a name="[99]"></a>OLED_WriteCommand</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, oled.o(i.OLED_WriteCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = OLED_WriteCommand &rArr; OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_SetCursor
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[a2]"></a>OLED_WriteData</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, oled.o(i.OLED_WriteData))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = OLED_WriteData &rArr; OLED_I2C_SendByte &rArr; OLED_W_SDA
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Stop
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_Start
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_I2C_SendByte
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
</UL>

<P><STRONG><a name="[8c]"></a>PWM_Init</STRONG> (Thumb, 156 bytes, Stack size 40 bytes, pwm.o(i.PWM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = PWM_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OCStructInit
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4Init
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3Init
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_InternalClockConfig
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
</UL>

<P><STRONG><a name="[8f]"></a>PWM_SetCompare3</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, pwm.o(i.PWM_SetCompare3))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PWM_SetCompare3
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare3
</UL>
<BR>[Called By]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetLeftSpeed
</UL>

<P><STRONG><a name="[90]"></a>PWM_SetCompare4</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, pwm.o(i.PWM_SetCompare4))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = PWM_SetCompare4
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare4
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetRightSpeed
</UL>

<P><STRONG><a name="[d]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[a4]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_Init
</UL>

<P><STRONG><a name="[63]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd))
<BR><BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_GPIO_Init
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_EXTI_Init
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_IO_Init
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Infrared_Init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_Init
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_Init
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_GPIO_Init
</UL>

<P><STRONG><a name="[bf]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 192 bytes, Stack size 12 bytes, stm32f10x_rcc.o(i.RCC_GetClocksFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
</UL>

<P><STRONG><a name="[b]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[ad]"></a>Self_Left</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, car.o(i.Self_Left))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Self_Left &rArr; Motor_SetRightSpeed &rArr; PWM_SetCompare4
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetRightSpeed
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetLeftSpeed
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[ae]"></a>Self_Right</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, car.o(i.Self_Right))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Self_Right &rArr; Motor_SetRightSpeed &rArr; PWM_SetCompare4
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetRightSpeed
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetLeftSpeed
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[af]"></a>Serial_Init</STRONG> (Thumb, 174 bytes, Stack size 32 bytes, serial.o(i.Serial_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = Serial_Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[d3]"></a>SysTick_CLKSourceConfig</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, misc.o(i.SysTick_CLKSourceConfig))
<BR><BR>[Called By]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[e]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f10x_it.o(i.SysTick_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>SystemInit</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, system_stm32f10x.o(i.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = SystemInit &rArr; SetSysClock &rArr; SetSysClockTo72
</UL>
<BR>[Calls]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(.text)
</UL>
<P><STRONG><a name="[2d]"></a>TIM4_IRQHandler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, main.o(i.TIM4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM4_IRQHandler &rArr; TIM_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[b8]"></a>TIM_ClearFlag</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ClearFlag))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_Init
</UL>

<P><STRONG><a name="[b6]"></a>TIM_ClearITPendingBit</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ClearITPendingBit))
<BR><BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_IRQHandler
</UL>

<P><STRONG><a name="[aa]"></a>TIM_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_Init
</UL>

<P><STRONG><a name="[b5]"></a>TIM_GetITStatus</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, stm32f10x_tim.o(i.TIM_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = TIM_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM4_IRQHandler
</UL>

<P><STRONG><a name="[b9]"></a>TIM_ITConfig</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_ITConfig))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_Init
</UL>

<P><STRONG><a name="[a5]"></a>TIM_InternalClockConfig</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_InternalClockConfig))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_Init
</UL>

<P><STRONG><a name="[a8]"></a>TIM_OC3Init</STRONG> (Thumb, 150 bytes, Stack size 16 bytes, stm32f10x_tim.o(i.TIM_OC3Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC3Init
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_Init
</UL>

<P><STRONG><a name="[a9]"></a>TIM_OC4Init</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, stm32f10x_tim.o(i.TIM_OC4Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC4Init
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_Init
</UL>

<P><STRONG><a name="[a7]"></a>TIM_OCStructInit</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_OCStructInit))
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_Init
</UL>

<P><STRONG><a name="[ab]"></a>TIM_SetCompare3</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_SetCompare3))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_SetCompare3
</UL>

<P><STRONG><a name="[ac]"></a>TIM_SetCompare4</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_SetCompare4))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_SetCompare4
</UL>

<P><STRONG><a name="[a6]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, stm32f10x_tim.o(i.TIM_TimeBaseInit))
<BR><BR>[Called By]<UL><LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_Init
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PWM_Init
</UL>

<P><STRONG><a name="[b7]"></a>Timer1_Init</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, timer.o(i.Timer1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Timer1_Init &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_InternalClockConfig
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ba]"></a>Turn_Left</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, car.o(i.Turn_Left))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Turn_Left &rArr; Motor_SetRightSpeed &rArr; PWM_SetCompare4
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetRightSpeed
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetLeftSpeed
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[bb]"></a>Turn_Right</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, car.o(i.Turn_Right))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Turn_Right &rArr; Motor_SetRightSpeed &rArr; PWM_SetCompare4
</UL>
<BR>[Calls]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetRightSpeed
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_SetLeftSpeed
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[34]"></a>USART1_IRQHandler</STRONG> (Thumb, 122 bytes, Stack size 8 bytes, main.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART1_IRQHandler &rArr; Turn_Right &rArr; Motor_SetRightSpeed &rArr; PWM_SetCompare4
</UL>
<BR>[Calls]<UL><LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Turn_Right
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Turn_Left
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Self_Right
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Self_Left
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Go_Back
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Go_Ahead
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Stop
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[be]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f10x_usart.o(i.USART_ClearITPendingBit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = USART_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[b2]"></a>USART_Cmd</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_Cmd))
<BR><BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_Init
</UL>

<P><STRONG><a name="[bc]"></a>USART_GetITStatus</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f10x_usart.o(i.USART_GetITStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[b1]"></a>USART_ITConfig</STRONG> (Thumb, 74 bytes, Stack size 20 bytes, stm32f10x_usart.o(i.USART_ITConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_Init
</UL>

<P><STRONG><a name="[b0]"></a>USART_Init</STRONG> (Thumb, 210 bytes, Stack size 56 bytes, stm32f10x_usart.o(i.USART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_Init
</UL>

<P><STRONG><a name="[bd]"></a>USART_ReceiveData</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f10x_usart.o(i.USART_ReceiveData))
<BR><BR>[Called By]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[a]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f10x_it.o(i.UsageFault_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_md.o(RESET)
</UL>
<P><STRONG><a name="[cd]"></a>__ARM_fpclassify</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, fpclassify.o(i.__ARM_fpclassify))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[c0]"></a>__kernel_poly</STRONG> (Thumb, 170 bytes, Stack size 24 bytes, poly.o(i.__kernel_poly))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[c2]"></a>__mathlib_dbl_infnan</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_infnan))
<BR><BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[c4]"></a>__mathlib_dbl_infnan2</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_infnan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __mathlib_dbl_infnan2 &rArr; __aeabi_dadd
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
</UL>

<P><STRONG><a name="[c5]"></a>__mathlib_dbl_invalid</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_invalid))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __mathlib_dbl_invalid &rArr; __aeabi_ddiv
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[c7]"></a>__mathlib_dbl_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dunder.o(i.__mathlib_dbl_underflow))
<BR><BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[6b]"></a>asin</STRONG> (Thumb, 572 bytes, Stack size 56 bytes, asin.o(i.asin))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = asin &rArr; sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[d1]"></a>atan</STRONG> (Thumb, 474 bytes, Stack size 40 bytes, atan.o(i.atan))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = atan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassify
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
</UL>

<P><STRONG><a name="[6f]"></a>atan2</STRONG> (Thumb, 346 bytes, Stack size 32 bytes, atan2.o(i.atan2))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = atan2 &rArr; atan &rArr; __kernel_poly &rArr; __aeabi_dmul
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[d2]"></a>delay_init</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, delay.o(i.delay_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = delay_init
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[ed]"></a>delay_ms</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, delay.o(i.delay_ms))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
</UL>

<P><STRONG><a name="[80]"></a>delay_us</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, delay.o(i.delay_us))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Send_Ack
<LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Read_Ack
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Stop
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Start
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Send_Byte
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Read_Byte
</UL>

<P><STRONG><a name="[d4]"></a>dmp_enable_6x_lp_quat</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_enable_6x_lp_quat))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = dmp_enable_6x_lp_quat &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[79]"></a>dmp_enable_feature</STRONG> (Thumb, 530 bytes, Stack size 24 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_enable_feature))
<BR><BR>[Stack]<UL><LI>Max Depth = 208 + Unknown Stack Size
<LI>Call Chain = dmp_enable_feature &rArr; dmp_set_tap_thresh &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time_multi
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_count
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_axes
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_timeout
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_time
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_thresh
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_gyro_cal
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[d7]"></a>dmp_enable_gyro_cal</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_enable_gyro_cal))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = dmp_enable_gyro_cal &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[e0]"></a>dmp_enable_lp_quat</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_enable_lp_quat))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + Unknown Stack Size
<LI>Call Chain = dmp_enable_lp_quat &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[76]"></a>dmp_load_motion_driver_firmware</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_load_motion_driver_firmware))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = dmp_load_motion_driver_firmware &rArr; mpu_load_firmware &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[65]"></a>dmp_read_fifo</STRONG> (Thumb, 450 bytes, Stack size 88 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_read_fifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 240<LI>Call Chain = dmp_read_fifo &rArr; mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;decode_gesture
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_fifo_stream
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[e4]"></a>dmp_set_accel_bias</STRONG> (Thumb, 300 bytes, Stack size 48 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_accel_bias))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = dmp_set_accel_bias &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_sens
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[7a]"></a>dmp_set_fifo_rate</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_fifo_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = dmp_set_fifo_rate &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[e6]"></a>dmp_set_gyro_bias</STRONG> (Thumb, 294 bytes, Stack size 24 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_gyro_bias))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = dmp_set_gyro_bias &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[78]"></a>dmp_set_orientation</STRONG> (Thumb, 290 bytes, Stack size 32 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_orientation))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = dmp_set_orientation &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[dd]"></a>dmp_set_shake_reject_thresh</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_thresh))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = dmp_set_shake_reject_thresh &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[de]"></a>dmp_set_shake_reject_time</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = dmp_set_shake_reject_time &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[df]"></a>dmp_set_shake_reject_timeout</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_shake_reject_timeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = dmp_set_shake_reject_timeout &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[d9]"></a>dmp_set_tap_axes</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_axes))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = dmp_set_tap_axes &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[da]"></a>dmp_set_tap_count</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_count))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = dmp_set_tap_count &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[d8]"></a>dmp_set_tap_thresh</STRONG> (Thumb, 396 bytes, Stack size 40 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_thresh))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = dmp_set_tap_thresh &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_fsr
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2uiz
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[db]"></a>dmp_set_tap_time</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = dmp_set_tap_time &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[dc]"></a>dmp_set_tap_time_multi</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, inv_mpu_dmp_motion_driver.o(i.dmp_set_tap_time_multi))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = dmp_set_tap_time_multi &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
</UL>

<P><STRONG><a name="[77]"></a>inv_orientation_matrix_to_scalar</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, inv_mpu.o(i.inv_orientation_matrix_to_scalar))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = inv_orientation_matrix_to_scalar
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inv_row_2_scale
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[f0]"></a>inv_row_2_scale</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, inv_mpu.o(i.inv_row_2_scale))
<BR><BR>[Called By]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;inv_orientation_matrix_to_scalar
</UL>

<P><STRONG><a name="[46]"></a>main</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 344 + Unknown Stack Size
<LI>Call Chain = main &rArr; MPU6050_DMP_Init &rArr; run_self_test &rArr; mpu_run_self_test &rArr; mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU_EXTI_Init
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer1_Init
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Infrared_Init
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Serial_Init
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Car_Init
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Update
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[ea]"></a>mpu6050_read</STRONG> (Thumb, 92 bytes, Stack size 24 bytes, mpu6050.o(i.mpu6050_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = mpu6050_read &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Stop
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Start
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Send_Byte
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Read_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_fifo_stream
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_accel_prod_shift
</UL>

<P><STRONG><a name="[ec]"></a>mpu6050_write</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, mpu6050.o(i.mpu6050_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Stop
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Start
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_IIC_Send_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_int_latched
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_gyro_fsr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_accel_fsr
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_int_enable
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
</UL>

<P><STRONG><a name="[74]"></a>mpu_configure_fifo</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_configure_fifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = mpu_configure_fifo &rArr; set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_int_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[e8]"></a>mpu_get_accel_fsr</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_accel_fsr))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[e5]"></a>mpu_get_accel_sens</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_accel_sens))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_accel_bias
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[fe]"></a>mpu_get_fifo_config</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_fifo_config))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[fb]"></a>mpu_get_gyro_fsr</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_gyro_fsr))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[ff]"></a>mpu_get_gyro_sens</STRONG> (Thumb, 58 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_gyro_sens))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[fc]"></a>mpu_get_lpf</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_lpf))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[fd]"></a>mpu_get_sample_rate</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, inv_mpu.o(i.mpu_get_sample_rate))
<BR><BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[72]"></a>mpu_init</STRONG> (Thumb, 396 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = mpu_init &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_gyro_fsr
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_accel_fsr
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[e1]"></a>mpu_load_firmware</STRONG> (Thumb, 180 bytes, Stack size 48 bytes, inv_mpu.o(i.mpu_load_firmware))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = mpu_load_firmware &rArr; mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_write_mem
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_mem
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_load_motion_driver_firmware
</UL>

<P><STRONG><a name="[f8]"></a>mpu_lp_accel_mode</STRONG> (Thumb, 218 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_lp_accel_mode))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_int_latched
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
</UL>

<P><STRONG><a name="[e2]"></a>mpu_read_fifo_stream</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, inv_mpu.o(i.mpu_read_fifo_stream))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = mpu_read_fifo_stream &rArr; mpu_reset_fifo &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
</UL>

<P><STRONG><a name="[f6]"></a>mpu_read_mem</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, inv_mpu.o(i.mpu_read_mem))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = mpu_read_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
</UL>

<P><STRONG><a name="[d6]"></a>mpu_reset_fifo</STRONG> (Thumb, 450 bytes, Stack size 8 bytes, inv_mpu.o(i.mpu_reset_fifo))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = mpu_reset_fifo &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_read_fifo_stream
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
</UL>

<P><STRONG><a name="[fa]"></a>mpu_run_self_test</STRONG> (Thumb, 278 bytes, Stack size 88 bytes, inv_mpu.o(i.mpu_run_self_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = mpu_run_self_test &rArr; mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_gyro_fsr
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_accel_fsr
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_sample_rate
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_lpf
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_gyro_fsr
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_fifo_config
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_fsr
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_st_biases
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[f3]"></a>mpu_set_accel_fsr</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_set_accel_fsr))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = mpu_set_accel_fsr &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
</UL>

<P><STRONG><a name="[f5]"></a>mpu_set_bypass</STRONG> (Thumb, 328 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_set_bypass))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = mpu_set_bypass &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
</UL>

<P><STRONG><a name="[7c]"></a>mpu_set_dmp_state</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_set_dmp_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_bypass
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_reset_fifo
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_int_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[f2]"></a>mpu_set_gyro_fsr</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_set_gyro_fsr))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = mpu_set_gyro_fsr &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
</UL>

<P><STRONG><a name="[f9]"></a>mpu_set_int_latched</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_set_int_latched))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = mpu_set_int_latched &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sensors
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
</UL>

<P><STRONG><a name="[f4]"></a>mpu_set_lpf</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_set_lpf))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = mpu_set_lpf &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_sample_rate
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
</UL>

<P><STRONG><a name="[75]"></a>mpu_set_sample_rate</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_set_sample_rate))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_lpf
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_lp_accel_mode
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[73]"></a>mpu_set_sensors</STRONG> (Thumb, 202 bytes, Stack size 16 bytes, inv_mpu.o(i.mpu_set_sensors))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = mpu_set_sensors &rArr; mpu_set_int_latched &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_int_latched
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_init
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[d5]"></a>mpu_write_mem</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, inv_mpu.o(i.mpu_write_mem))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = mpu_write_mem &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time_multi
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_time
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_count
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_axes
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_timeout
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_time
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_shake_reject_thresh
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_lp_quat
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_gyro_cal
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_6x_lp_quat
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_orientation
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_gyro_bias
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_fifo_rate
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_accel_bias
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_enable_feature
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_load_firmware
</UL>

<P><STRONG><a name="[91]"></a>power</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, sys.o(i.power))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = power
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;My_GPIO_Init
</UL>

<P><STRONG><a name="[7b]"></a>run_self_test</STRONG> (Thumb, 148 bytes, Stack size 48 bytes, inv_mpu.o(i.run_self_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 336<LI>Call Chain = run_self_test &rArr; mpu_run_self_test &rArr; mpu_set_dmp_state &rArr; mpu_set_sample_rate &rArr; mpu_lp_accel_mode &rArr; mpu_configure_fifo &rArr; set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_gyro_bias
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_accel_bias
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_gyro_sens
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_get_accel_sens
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Init
</UL>

<P><STRONG><a name="[cf]"></a>sqrt</STRONG> (Thumb, 76 bytes, Stack size 24 bytes, sqrt.o(i.sqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = sqrt &rArr; _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
</UL>

<P><STRONG><a name="[6d]"></a>__aeabi_d2f</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[102]"></a>_d2f</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, d2f.o(x$fpl$d2f), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[c1]"></a>__aeabi_dadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan2
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[105]"></a>_dadd</STRONG> (Thumb, 332 bytes, Stack size 16 bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[108]"></a>__fpl_dcheck_NaN1</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, dcheck1.o(x$fpl$dcheck1))
<BR><BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_scalbn
</UL>

<P><STRONG><a name="[c6]"></a>__aeabi_ddiv</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_ddiv
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_invalid
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[10a]"></a>_ddiv</STRONG> (Thumb, 552 bytes, Stack size 32 bytes, ddiv.o(x$fpl$ddiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[6c]"></a>__aeabi_dmul</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dmul
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__kernel_poly
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[10b]"></a>_dmul</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, dmul.o(x$fpl$dmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[104]"></a>__fpl_dnaninf</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, dnaninf.o(x$fpl$dnaninf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsqrt
</UL>

<P><STRONG><a name="[107]"></a>__fpl_dretinf</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dretinf.o(x$fpl$dretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dmul
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ddiv
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[ce]"></a>__aeabi_drsub</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_drsub
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[10c]"></a>_drsb</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, daddsub_clz.o(x$fpl$drsb), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub1
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
</UL>

<P><STRONG><a name="[101]"></a>_dsqrt</STRONG> (Thumb, 456 bytes, Stack size 24 bytes, dsqrt_noumaal.o(x$fpl$dsqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _dsqrt &rArr; __fpl_dnaninf
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;sqrt
</UL>

<P><STRONG><a name="[d0]"></a>__aeabi_dsub</STRONG> (Thumb, 0 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __aeabi_dsub
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan2
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[10e]"></a>_dsub</STRONG> (Thumb, 464 bytes, Stack size 32 bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd1
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dnaninf
</UL>

<P><STRONG><a name="[6a]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[10f]"></a>_f2d</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, f2d.o(x$fpl$f2d), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dretinf
</UL>

<P><STRONG><a name="[69]"></a>__aeabi_fadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
</UL>

<P><STRONG><a name="[111]"></a>_fadd</STRONG> (Thumb, 196 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[115]"></a>__fpl_fcmp_Inf</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, fcmpi.o(x$fpl$fcmpinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpeq
</UL>

<P><STRONG><a name="[67]"></a>__aeabi_fdiv</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fdiv.o(x$fpl$fdiv))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[113]"></a>_fdiv</STRONG> (Thumb, 384 bytes, Stack size 16 bytes, fdiv.o(x$fpl$fdiv), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[ca]"></a>__aeabi_cfcmpeq</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, feqf.o(x$fpl$feqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_cfcmpeq
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[114]"></a>_fcmpeq</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, feqf.o(x$fpl$feqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_Inf
</UL>

<P><STRONG><a name="[100]"></a>__aeabi_f2iz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffix.o(x$fpl$ffix))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
</UL>

<P><STRONG><a name="[116]"></a>_ffix</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, ffix.o(x$fpl$ffix), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[e9]"></a>__aeabi_f2uiz</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, ffixu.o(x$fpl$ffixu))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_f2uiz
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
</UL>

<P><STRONG><a name="[117]"></a>_ffixu</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, ffixu.o(x$fpl$ffixu), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[66]"></a>__aeabi_i2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$fflt))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[150]"></a>_fflt</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$fflt), UNUSED)

<P><STRONG><a name="[e7]"></a>__aeabi_ui2f</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$ffltu))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
</UL>

<P><STRONG><a name="[151]"></a>_ffltu</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fflt_clz.o(x$fpl$ffltu), UNUSED)

<P><STRONG><a name="[cc]"></a>__aeabi_cfcmple</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fleqf.o(x$fpl$fleqf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_cfcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[118]"></a>_fcmple</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, fleqf.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_Inf
</UL>

<P><STRONG><a name="[11b]"></a>__fpl_fcmple_InfNaN</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, fleqf.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frcmple
</UL>

<P><STRONG><a name="[68]"></a>__aeabi_fmul</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_set_tap_thresh
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;run_self_test
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_accel_prod_shift
</UL>

<P><STRONG><a name="[119]"></a>_fmul</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fretinf
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[110]"></a>__fpl_fnaninf</STRONG> (Thumb, 140 bytes, Stack size 8 bytes, fnaninf.o(x$fpl$fnaninf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fmul
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ffixu
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_ffix
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpeq
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_f2d
</UL>

<P><STRONG><a name="[103]"></a>__fpl_fretinf</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, fretinf.o(x$fpl$fretinf), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fmul
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_d2f
</UL>

<P><STRONG><a name="[cb]"></a>__aeabi_cfrcmple</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, frleqf.o(x$fpl$frleqf))
<BR><BR>[Called By]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[11a]"></a>_frcmple</STRONG> (Thumb, 98 bytes, Stack size 0 bytes, frleqf.o(x$fpl$frleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmple_InfNaN
</UL>

<P><STRONG><a name="[6e]"></a>__aeabi_fsub</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MPU6050_DMP_Get_Data
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gyro_self_test
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[11c]"></a>_fsub</STRONG> (Thumb, 234 bytes, Stack size 16 bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fnaninf
</UL>

<P><STRONG><a name="[109]"></a>__fpl_return_NaN</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, retnan.o(x$fpl$retnan))
<BR><BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>

<P><STRONG><a name="[c3]"></a>__ARM_scalbn</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, scalbn.o(x$fpl$scalbn))
<BR><BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_dcheck_NaN1
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;asin
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_underflow
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_dbl_infnan
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;atan
</UL>

<P><STRONG><a name="[11e]"></a>__fpl_cmpreturn</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, trapv.o(x$fpl$trapveneer))
<BR><BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[b3]"></a>SetSysClock</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_stm32f10x.o(i.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SetSysClock &rArr; SetSysClockTo72
</UL>
<BR>[Calls]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClockTo72
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[b4]"></a>SetSysClockTo72</STRONG> (Thumb, 214 bytes, Stack size 12 bytes, system_stm32f10x.o(i.SetSysClockTo72))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClockTo72
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>

<P><STRONG><a name="[c8]"></a>accel_self_test</STRONG> (Thumb, 164 bytes, Stack size 48 bytes, inv_mpu.o(i.accel_self_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = accel_self_test &rArr; get_accel_prod_shift &rArr; mpu6050_read &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_accel_prod_shift
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmpeq
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[c9]"></a>get_accel_prod_shift</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, inv_mpu.o(i.get_accel_prod_shift))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = get_accel_prod_shift &rArr; mpu6050_read &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;accel_self_test
</UL>

<P><STRONG><a name="[eb]"></a>get_st_biases</STRONG> (Thumb, 1132 bytes, Stack size 64 bytes, inv_mpu.o(i.get_st_biases))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = get_st_biases &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[ef]"></a>gyro_self_test</STRONG> (Thumb, 256 bytes, Stack size 48 bytes, inv_mpu.o(i.gyro_self_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = gyro_self_test &rArr; mpu6050_read &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_read
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfrcmple
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cfcmple
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_run_self_test
</UL>

<P><STRONG><a name="[f1]"></a>set_int_enable</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, inv_mpu.o(i.set_int_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = set_int_enable &rArr; mpu6050_write &rArr; MPU6050_IIC_Send_Byte &rArr; MPU6050_IIC_Read_Ack &rArr; MPU6050_IIC_Stop &rArr; MPU6050_IIC_SDA_IO_OUT &rArr; My_GPIO_Init &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu6050_write
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_set_dmp_state
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mpu_configure_fifo
</UL>

<P><STRONG><a name="[e3]"></a>decode_gesture</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, inv_mpu_dmp_motion_driver.o(i.decode_gesture))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = decode_gesture
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dmp_read_fifo
</UL>

<P><STRONG><a name="[10d]"></a>_dadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dsub
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
</UL>

<P><STRONG><a name="[106]"></a>_dsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, daddsub_clz.o(x$fpl$dsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_drsb
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_dadd
</UL>

<P><STRONG><a name="[11d]"></a>_fadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub_clz.o(x$fpl$fadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
</UL>

<P><STRONG><a name="[112]"></a>_fsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub_clz.o(x$fpl$fsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
